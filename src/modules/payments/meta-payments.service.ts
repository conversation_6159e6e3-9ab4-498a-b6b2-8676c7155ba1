import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { PaymentsDocumentDbService } from './payments-db/payments-db.service';
import {
  MetaPaymentStatus,
  PaymentProviders,
  PaymentResult,
  UserMetaPaymentDocument,
  UserMetaPaymentModel,
} from './meta-payment.model';
import { EventsService } from '../events/events.service';
import { RABBITMQ } from '../events/exchanges-and-queues';
import { PaginationQueryDto } from '../common/pagination/pagination-query.dto';
import { PlansService } from '../plans/plans.service';
import { UserService } from '../user/user.service';
import { CouponService } from '../coupon/services/coupon.service';
import { ValueType } from '../coupon/helper/helper';
import { CouponModel } from '../coupon/models/coupon.model';
import {
  PaymentsAggregatorDocumentDbService,
} from '../document-db-aggregations/aggregators/payments-aggregator-db.service';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class MetaPaymentsService {
  constructor(
    private readonly logger: Logger,
    private readonly paymentsDocumentDbService: PaymentsDocumentDbService,
    private readonly eventsService: EventsService,
    @Inject(forwardRef(() => PlansService)) // todo: this should be removed from here!
    private readonly plansService: PlansService, // todo: this should be removed from here!
    private readonly userService: UserService,
    private readonly couponService: CouponService,
    private readonly paymentsAggregatorDocumentDbService: PaymentsAggregatorDocumentDbService,
    @InjectModel(UserMetaPaymentModel.name)
    private readonly userMetaPaymentModel: Model<UserMetaPaymentDocument>,
  ) {}

  async handleCompletedMetaPayment(
    payment: UserMetaPaymentModel | UserMetaPaymentDocument,
  ) {
    const user = await this.userService.getUserById(payment.userId);

    await Promise.all([
      !user.isCardVerified
        ? this.userService.setIsCardVerified(payment.userId)
        : null,
      this.eventsService.send(
        RABBITMQ.plans.queues.handleUserPaymentComplete,
        payment,
      ),
      this.eventsService.send(RABBITMQ.comissions.queues.comissionCompleted, {
        userId: payment.userId,
        amount: payment.amountUsd,
        paymentMethod: payment.providerId,
        paymentId: payment.paymentId,
      }),
      // send other events here if needed
    ]);
  }

  async createMetaPayment(paymentIn: UserMetaPaymentModel) {
    const invoiceNumber = await this.generateInvoiceNumber();

    const paymentWithInvoice = {
      ...paymentIn,
      invoiceNumber,
    };

    const payment =
      await this.paymentsDocumentDbService.createUserMetaPayment(
        paymentWithInvoice,
      );

    if (payment.status === MetaPaymentStatus.Completed) {
      await this.handleCompletedMetaPayment(payment);
    }

    return payment;
  }
  private async generateInvoiceNumber(): Promise<string> {
    try {
      const lastInvoice =
        await this.paymentsAggregatorDocumentDbService.getLastInvoiceNumber();

      if (!lastInvoice || !lastInvoice.invoiceNumber) {
        return 'INV10000001';
      }

      const numericPart = lastInvoice.invoiceNumber.substring(3);
      const lastNumber = parseInt(numericPart, 10);

      if (isNaN(lastNumber)) {
        return 'INV10000001';
      }

      return `INV${lastNumber + 1}`;
    } catch (error) {
      console.error('Error generating invoice number:', error);
      return 'INV10000001';
    }
  }

  async getMetaPaymentsByUserId(userId: string, query: PaginationQueryDto) {
    return await this.paymentsDocumentDbService.getUserMetaPaymentsByUserId(
      userId,
      query,
    );
  }

  async getMetaPayments(query: PaginationQueryDto) {
    return await this.paymentsDocumentDbService.getMetaPayments(query);
  }

  async getMetaPaymentByPaymentIdAndProvider(
    paymentId: string,
    providerId: PaymentProviders,
  ): Promise<UserMetaPaymentDocument | null> {
    return await this.paymentsDocumentDbService.getMetaPayment(
      providerId,
      paymentId,
    );
  }

  async updateMetaPaymentStatus(
    providerId: PaymentProviders,
    paymentId: string,
    status: MetaPaymentStatus,
    metadata?: Record<string, any>,
  ) {
    const existingPayment = await this.paymentsDocumentDbService.getMetaPayment(
      providerId,
      paymentId,
    );

    const newPayment =
      await this.paymentsDocumentDbService.updateUserMetaPaymentStatus(
        providerId,
        paymentId,
        status,
        metadata,
      );

    if (
      status === MetaPaymentStatus.Completed &&
      existingPayment.status !== MetaPaymentStatus.Completed
    ) {
      await this.handleCompletedMetaPayment(newPayment);
    }

    return newPayment;
  }

  async getLastPayment(
    paymentType: 'subscription' | 'topup',
    userId: string,
  ): Promise<PaymentResult> {
    if (paymentType === 'subscription') {
      const data =
        await this.paymentsDocumentDbService.getLastSubscriptionPayment(userId);

      const plan = await this.plansService.getPlanByPriceId(
        data.providerPlanId,
      );

      // todo: add types
      return {
        date: data.createdAt,
        amount: plan.price,
        tokens: plan.tokenAllocation,
        planName: plan.name,
        paymentId: data.paymentId,
        currency: data.currency || 'USD',
      };
    }

    if (paymentType === 'topup') {
      const paymentData =
        await this.paymentsDocumentDbService.getLastTopUpPayment(userId);
      let tokens = await this.plansService.getTokensFromPrice(
        paymentData.amountUsd,
      );

      if (paymentData?.couponCode) {
        const coupon = await this.couponService.getCoupon(
          paymentData.couponCode,
        );
        tokens = await this.calculateTokensWithCoupon(
          tokens,
          coupon,
          paymentData.amountUsd,
        );
      }
      return this.formatTopUpResponse(paymentData, tokens);
    }

    return null;
  }

  private async calculateTokensWithCoupon(
    baseTokens: number,
    coupon: CouponModel,
    originalAmountUsd: number,
  ): Promise<number> {
    switch (coupon.valueType) {
      case ValueType.TOKENS:
        return baseTokens + coupon.value;

      case ValueType.PERCENTAGE_EXTRA_TOKENS:
        return baseTokens + Math.floor((baseTokens * coupon.value) / 100);

      case ValueType.PERCENTAGE_TOKEN_DISCOUNT:
        const originalAmount = originalAmountUsd / (1 - coupon.value / 100);
        return await this.plansService.getTokensFromPrice(originalAmount);

      default:
        return baseTokens;
    }
  }

  private formatTopUpResponse(
    paymentData: UserMetaPaymentDocument,
    tokens: number,
  ) {
    return {
      date: paymentData.createdAt,
      amount: paymentData.amountUsd,
      tokens,
      paymentId: paymentData.paymentId,
      currency: paymentData.currency || 'USD',
      couponUsed: paymentData.couponCode || null,
    };
  }

  async getCryptoMetaPayments(
    userId: string,
  ): Promise<UserMetaPaymentDocument[]> {
    return await this.paymentsDocumentDbService.getCryptoMetaPayments(userId);
  }

  async getMetaPaymentBySubscriptionId(
    subscriptionId: string,
  ): Promise<UserMetaPaymentDocument | null> {
    return await this.paymentsDocumentDbService.getMetaPaymentBySubscriptionId(
      subscriptionId,
    );
  }

  async getMetaPaymentByPaymentIntentId(
    paymentIntentId: string,
  ): Promise<UserMetaPaymentDocument | null> {
    return await this.paymentsDocumentDbService.getMetaPaymentByPaymentIntentId(
      paymentIntentId,
    );
  }

  async getCryptoMetaPaymentById(
    paymentId: string,
  ): Promise<UserMetaPaymentDocument | null> {
    return await this.paymentsDocumentDbService.getCryptoMetaPaymentById(
      paymentId,
    );
  }

  async migrateInvoiceNumbers() {
    try {
      const allPayments = await this.userMetaPaymentModel
        .find({})
        .sort({ createdAt: 1 })
        .exec();

      if (!allPayments || allPayments.length === 0) {
        return { updated: 0, skipped: 0, errors: 0 };
      }

      let currentInvoiceNumber = 10000000;
      let updated = 0;
      const skipped = 0;
      let errors = 0;

      const batchSize = 100;
      for (let i = 0; i < allPayments.length; i += batchSize) {
        const batch = allPayments.slice(i, i + batchSize);
        const bulkOps = [];

        for (const payment of batch) {
          try {
            const newInvoiceNumber = `INV${currentInvoiceNumber}`;

            if (payment.invoiceNumber) {
              bulkOps.push({
                updateOne: {
                  filter: { _id: payment._id },
                  update: { $set: { invoiceNumber: newInvoiceNumber } },
                },
              });
              updated++;
            } else {
              bulkOps.push({
                updateOne: {
                  filter: { _id: payment._id },
                  update: { $set: { invoiceNumber: newInvoiceNumber } },
                },
              });
              updated++;
            }

            currentInvoiceNumber++;
          } catch (error) {
            console.error(`Error processing payment ${payment._id}:`, error);
            errors++;
          }
        }

        if (bulkOps.length > 0) {
          await this.userMetaPaymentModel.bulkWrite(bulkOps);
        }
      }

      return { updated, skipped, errors, totalProcessed: allPayments.length };
    } catch (error) {
      console.error('Error in migrateInvoiceNumbers:', error);
      throw error;
    }
  }

  async getMetaPaymentByMetadataPlanId(
    planId: string,
  ): Promise<UserMetaPaymentDocument | null> {
    return await this.paymentsDocumentDbService.getMetaPaymentByMetadataPlanId(
      planId,
    );
  }
}
