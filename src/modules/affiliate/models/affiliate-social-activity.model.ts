import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { Field, ObjectType } from '@nestjs/graphql';

export type AffiliateSocialActivityDocument = AffiliateSocialActivityModel &
  Document;

@ObjectType() // GraphQL
@Schema({ collection: 'affiliate-social-activities', timestamps: true }) // MongoDB
export class AffiliateSocialActivityModel {
  @Field(() => String, { nullable: true })
  _id?: mongoose.Types.ObjectId;

  @Field(() => String)
  @Prop({ required: true })
  userId: string;

  @Field(() => String)
  @Prop({ required: true })
  contentId: string;

  @Field(() => String)
  @Prop({
    required: true,
    enum: ['engagement', 'content'],
  })
  activityType: string;

  @Field(() => String)
  @Prop({ required: true })
  platform: string;

  @Field(() => String)
  @Prop({ required: true })
  submissionData: string;

  @Field(() => String)
  @Prop({
    required: true,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
  })
  status: string;

  @Field(() => String, { nullable: true })
  @Prop()
  reviewedBy?: string;

  @Field(() => Date, { nullable: true })
  @Prop()
  reviewedAt?: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  transactionId?: string;

  @Field(() => Object)
  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  // automatically managed by mongo
  @Field(() => Date)
  @Prop()
  createdAt?: Date;

  @Field(() => Date)
  @Prop()
  updatedAt?: Date;

  constructor(init?: Partial<AffiliateSocialActivityModel>) {
    Object.assign(this, init);
  }
}

export const AffiliateSocialActivitySchema = SchemaFactory.createForClass(
  AffiliateSocialActivityModel,
)
  .index({ userId: 1 })
  .index({ contentId: 1 })
  .index({ activityType: 1 })
  .index({ platform: 1 })
  .index({ status: 1 })
  .index({ createdAt: -1 })
  .set('timestamps', true);
