import { Injectable, Logger } from '@nestjs/common';
import { ReferralModel } from './models/referral.model';
import { ReferralRecordModel } from './models/referral-record.model';
import { CacheInfo } from '../caching/cache.info';
import { JohnyCacheService } from 'johnycash';
import { ReferralsDocumentDbService } from './referrals-db/referrals-db.service';
import { UserService } from '../user/user.service';
import { NotFoundError } from 'src/modules/common/responses/http.responses';
import { PaginationQueryDto } from '../common/pagination/pagination-query.dto';

@Injectable()
export class ReferralsService {
  constructor(
    private readonly logger: Logger,
    private readonly referralsDocumentDbService: ReferralsDocumentDbService,
    private readonly cachingService: JohnyCacheService,
    private readonly userService: UserService,
  ) {}

  private generateReferralCode(): string {
    // 8 characters long, uppercase letters and numbers
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    const charactersLength = characters.length;
    for (let i = 0; i < 8; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  async createReferralCode(userId: string, expires_at?: Date) {
    let retries = 5;
    do {
      const code = this.generateReferralCode();
      const existingReferral =
        await this.referralsDocumentDbService.getRefferalByCode(code);
      if (!existingReferral) {
        const referral = new ReferralModel({
          user: userId,
          code,
          expires_at,
        });
        return await this.referralsDocumentDbService.createReferral(referral);
      }
    } while (retries-- > 0);

    this.logger.error('Failed to generate unique referral code');
  }

  async useRefferalCode(userIdOrEmail: string, code: string) {
    // remove all white spaces from referral
    code = code.replace(/\s/g, '');

    const [referral, alreadyRefereed] = await Promise.all([
      this.referralsDocumentDbService.getRefferalByCode(code),
      this.referralsDocumentDbService.getReferralRecordByReferee(userIdOrEmail),
    ]);

    if (!referral) {
      return;
    }

    if (alreadyRefereed || referral?.expires_at < new Date()) {
      this.logger.debug('Invalid referral code');
      return;
    }

    const referralRecord = new ReferralRecordModel({
      referee: userIdOrEmail,
      referrer: referral.user,
      referralCodeId: referral._id.toString(),
    });

    await this.referralsDocumentDbService.createReferralRecord(referralRecord);
  }

  async getUserReferrals(userId: string) {
    return this.referralsDocumentDbService.getActiveReferralsByUserId(userId);
  }

  async getUserReferralsV2(userId: string, query: PaginationQueryDto) {
    return this.referralsDocumentDbService.getActiveReferralsByUserIdWithPagination(
      userId,
      query,
    );
  }
  async getFirstUserReferral(user: string) {
    return await this.referralsDocumentDbService.findFirstReferralByUser(user);
  }

  async getOrCreateUserDefaultReferralRaw(userId: string) {
    const defaultReferrals =
      await this.referralsDocumentDbService.getActiveReferralsByUserId(userId);

    if (defaultReferrals?.length) {
      return defaultReferrals[0];
    }

    return await this.createReferralCode(userId);
  }

  async deleteUserReferral(userId: string, referralId: string) {
    return this.referralsDocumentDbService.deleteUserReferral(
      userId,
      referralId,
    );
  }

  async getUserDefaultReferral(userId: string) {
    const cacheSettings = CacheInfo.UserDefaultReferralCacheSettings(userId);
    return await this.cachingService.getOrSetCache(cacheSettings, async () => {
      const referral = await this.getOrCreateUserDefaultReferralRaw(userId);
      return referral;
    });
  }

  async getReferralRecordsWithPagination(page: number, limit: number) {
    return await this.referralsDocumentDbService.getReferralRecordsWithPagination(
      page,
      limit,
    );
  }

  async getReferralsByUserIdOrEmailWithPagination(
    page: number,
    limit: number,
    query: any = {},
    sortOptions: any = { createdAt: -1 },
  ) {
    return await this.referralsDocumentDbService.getReferralsByUserIdOrEmailWithPagination(
      page,
      limit,
      query,
      sortOptions,
    );
  }

  async countReferralsByReferrer(referrerId: string) {
    return await this.referralsDocumentDbService.countReferralsByReferrer(
      referrerId,
    );
  }

  async getReferralRecordByReferee(userId: string) {
    return await this.referralsDocumentDbService.getReferralRecordByReferee(
      userId,
    );
  }

  async getReferralsByReferrerId(referrerId: string) {
    return await this.referralsDocumentDbService.getReferralsByReferrerId(
      referrerId,
    );
  }

  async createReferralRecord(referralRecord: ReferralRecordModel) {
    return await this.referralsDocumentDbService.createReferralRecord(
      referralRecord,
    );
  }

  async updateReferralRecord(
    recordId: string,
    updateData: Partial<ReferralRecordModel>,
  ) {
    return await this.referralsDocumentDbService.updateReferralRecord(
      recordId,
      updateData,
    );
  }

  async getReferrerByUserId(userId: string) {
    const referralRecord =
      await this.referralsDocumentDbService.getReferrerByUserId(userId);

    if (!referralRecord || !referralRecord.referrer) {
      throw new NotFoundError('Referrer not found.');
    }

    const referrerId = referralRecord.referrer;

    const referrerUser = await this.userService.getUserById(referrerId);

    if (!referrerUser) {
      throw new NotFoundError('Referrer user not found.');
    }

    return referrerUser.username;
  }
  async getReferrerByUserIdForMonitoring(
    userId: string,
  ): Promise<string | null> {
    const referralRecord =
      await this.referralsDocumentDbService.getReferrerByUserId(userId);
    if (!referralRecord?.referrer) return null;

    const referrerUser = await this.userService.getUserById(
      referralRecord.referrer,
    );
    return referrerUser?.username || null;
  }

  async isReferral(recipientId: string, senderId: string): Promise<boolean> {
    try {
      const referralRecord =
        await this.referralsDocumentDbService.getReferrerByUserId(recipientId);
      return referralRecord?.referrer === senderId;
    } catch {
      return false;
    }
  }
}
