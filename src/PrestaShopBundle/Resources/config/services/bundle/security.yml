parameters:
  prestashop.security.voter.product.class: PrestaShopBundle\Security\Voter\PageVoter

services:
  _defaults:
    public: true

  prestashop.security.admin.provider:
    class: PrestaShopBundle\Security\Admin\EmployeeProvider
    arguments:
      - "@prestashop.adapter.legacy.context"
      - "@prestashop.static_cache.adapter"

  prestashop.security.role.dynamic_role_hierarchy:
    class: PrestaShopBundle\Security\Role\DynamicRoleHierarchy

  prestashop.security.voter.product:
    class: "%prestashop.security.voter.product.class%"
    tags:
      - { name: security.voter }
    public: false

  PrestaShop\PrestaShop\Core\Security\TokenAuthenticator:
    public: false
    arguments:
      - '@PrestaShop\PrestaShop\Core\OAuth2\OAuth2Interface'
      - '@Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory'

  PrestaShopBundle\Security\OAuth2\ResourceServer:
    public: false
    arguments:
      - '@League\OAuth2\Server\ResourceServer'
      - '@security.user.provider.concrete.oauth2'

  PrestaShop\PrestaShop\Core\OAuth2\OAuth2Interface:
    alias: PrestaShopBundle\Security\OAuth2\ResourceServer
    public: false
