{#**
 * 2007-2019 PrestaShop and Contributors
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://www.prestashop.com for more information.
 *
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2007-2019 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 * International Registered Trademark & Property of PrestaShop SA
 *#}

{% if customerInformation.sentEmailsInformation is not empty %}
  <div class="card">
    <h3 class="card-header">
      <i class="material-icons">mail_outline</i>
      {{ 'Last emails'|trans({}, 'Admin.Orderscustomers.Feature') }}
      <span class="badge badge-primary rounded">{{ customerInformation.productsInformation.boughtProductsInformation|length }}</span>
    </h3>
    <div class="card-body">
      <table class="table">
        <thead>
        <tr>
          <th>{{ 'Date'|trans({}, 'Admin.Global') }}</th>
          <th>{{ 'Language'|trans({}, 'Admin.Global') }}</th>
          <th>{{ 'Subject'|trans({}, 'Admin.Global') }}</th>
          <th>{{ 'Template'|trans({}, 'Admin.Global') }}</th>
        </tr>
        </thead>
        <tbody>
        {% for sentEmail in customerInformation.sentEmailsInformation %}
          <tr>
            <td>{{ sentEmail.date }}</td>
            <td>{{ sentEmail.language }}</td>
            <td>{{ sentEmail.subject }}</td>
            <td>{{ sentEmail.template }}</td>
          </tr>
        {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
{% endif %}
