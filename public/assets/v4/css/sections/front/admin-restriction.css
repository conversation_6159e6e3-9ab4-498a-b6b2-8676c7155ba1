.admin-restriction-section {
    background-color: #fff;
    position: relative;
    height: 100vh;
    padding: 0 25px;
}
.admin-restriction-container {
    max-width: 900px;
    margin: 0 auto;
    height: 100%;
    align-items: center;
    display: flex;
    width: 100%;
    justify-content: space-between;
    gap: 48px;
    position: relative;
}
.admin-restriction-container .admin-restriction-image-container {
    width: 45%;
}
.admin-restriction-container .admin-restriction-image-container img {
    width: 100%;
    object-fit: contain;
}
.admin-restriction-container .admin-restriction-content {
    width: 50%;
}
.admin-restriction-container .title-admin-restriction {
    color: #151A2D;
    font-family: "Power Grotesk", sans-serif;
    font-size: clamp(2rem, 5vw, 32px);
    font-style: normal;
    font-weight: 400;
    line-height: clamp(3rem, 6vw, 48px);
    margin-bottom: 26px;
}
.admin-restriction-container .info {
    color: #151A2D;
    font-family: "Inter", sans-serif;
    font-size: clamp(1.125rem, 3vw, 18px);
    font-style: normal;
    font-weight: 400;
    line-height: clamp(1.75rem, 4vw, 28px);

    margin-bottom: 48px;
}
.admin-restriction-container .back-button {
    color: #151A2D;
    font-family: "Power Grotesk", sans-serif;
    font-size: clamp(1.5rem, 4vw, 24px);
    font-style: normal;
    font-weight: 400;
    line-height: clamp(2rem, 5vw, 32px);
    margin-bottom: 48px;
    cursor: pointer;
    gap: 16px;
    align-items: center;
    display: flex;
}
.admin-restriction-container .back-button img {
    border-radius: 50%;
    padding: 8px;
    border: 0.5px solid #E0E2E7;
    background: #FFF;

}
.admin-restriction-container .admin-restriction-content-container {
    display: flex;
    justify-content: space-between;
    gap: 48px;
    position: relative;
    z-index: 2;
}
.admin-restriction-container .admin-restriction-content-container a {
    max-width: 300px;
    border-radius: 8px;
    background: #C9FD5D;
    display: flex;
    padding: 16px 24px;
    justify-content: center;
    align-items: center;
    gap: 16px;
    flex: 1 0 0;
    align-self: stretch;
    color: #151A2D;
    text-align: center;
    font-family: "Inter", sans-serif;
    font-size: clamp(1.125rem, 3vw, 18px);
    font-style: normal;
    font-weight: 400;
    line-height: clamp(1.75rem, 4vw, 28px);

}
.admin-restriction-section .colored-dots {
    background-color: transparent;
    background-image: url('/assets/v4/img/hexagon-white-bg.svg');
}
.admin-restriction-container .navbar-text {
    position: absolute;
    top: 47px;
    left: 0;
    height: 45px;
}
.admin-restriction-container .privacy-policy-text-container {
    position: absolute;
    bottom: 48px;
    left: 0;
    display: flex;
    gap: 48px;
}
.admin-restriction-container .privacy-policy-text-container a {
    color: #667085;
    font-family: "Inter", sans-serif;
    font-size: clamp(1rem, 2.5vw, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: clamp(1.5rem, 4vw, 24px);
    text-decoration: unset;
}
.admin-restriction-section .lower-page-image {
    position: absolute;
    right: 0;
    bottom: 0;
}
@media screen and (max-width: 850px){
    .admin-restriction-container,
    .admin-restriction-section {
        height: 100%;
        min-height: 100svh;
    }
    .admin-restriction-container .admin-restriction-content-container {
        flex-direction: column;
        margin: 55px auto 100px;
        max-width: 450px;
    }
    .admin-restriction-container .admin-restriction-image-container,
    .admin-restriction-container .admin-restriction-content {
        width: 100%;
    }
    .admin-restriction-container .admin-restriction-image-container{
        height: 300px;
    }
    .admin-restriction-container .admin-restriction-image-container img {
        height: 100%;
    }
    .admin-restriction-container .navbar-text {
        top: 20px;
        left: 25px;
    }
    .admin-restriction-container .privacy-policy-text-container {
        gap: 20px;
        z-index: 2;
    }

}