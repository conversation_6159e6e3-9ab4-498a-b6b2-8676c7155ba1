{**
 * 2007-2018 PrestaShop
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License 3.0 (AFL-3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to http://www.prestashop.com for more information.
 *
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2007-2018 PrestaShop SA
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 * International Registered Trademark & Property of PrestaShop SA
 *}
<div id="js-product-list">
    {if isset($listing.products_grouped) && $listing.products_grouped}
        {$groups_with_products = 0}
        {foreach from=$listing.products_grouped item="group" key=key}
            {if sizeof($group)}
                {$groups_with_products = $groups_with_products + 1}
            {/if}
        {/foreach}
        {foreach from=$listing.products_grouped item="group" key=key}
            {if sizeof($group)}
                {$remove_cartus = false}
                {if $key == 'black'}
                    {$add = 'negru'}
                {elseif $key == 'magenta'}
                    {$add = 'magenta'}
                {elseif $key == 'light_magenta'}
                    {$add = 'light magenta'}
                {elseif $key == 'cyan'}
                    {$add = 'cyan'}
                {elseif $key == 'light_cyan'}
                    {$add = 'light cyan'}
                {elseif $key == 'yellow'}
                    {$add = 'yellow'}
                {elseif $key == 'color'}
                    {$add = 'color'}
                {elseif $key == 'black_color'}
                    {$add = 'negru + color'}
                {elseif $key == 'uncategorized'}
                    {$add = false}
                {else}
                    {$add = $key}
                    {$remove_cartus = true}
                {/if}
                {if $add && !$remove_cartus}
                    {$subtitle = str_replace('Cartus cerneala', 'Cartus cerneala '|cat:$add|cat:' pentru ', $category.name)}
                    {$subtitle = str_replace('Cartus toner', 'Cartus toner '|cat:$add|cat:' pentru ', $subtitle)}
                {elseif $add && $remove_cartus}
                    {if $category.id_parent == 16323}
                        {$subtitle = str_replace('Cartus cerneala', $add|cat:' pentru ', $key)}
                        {$subtitle = str_replace('Cartus toner', $add|cat:' pentru ', $subtitle)}
                    {else}
                        {$subtitle = str_replace('Cartus cerneala', $add|cat:' pentru ', $category.name)}
                        {$subtitle = str_replace('Cartus toner', $add|cat:' pentru ', $subtitle)}
                    {/if}
                {else}
                    {if $key == 'uncategorized'}
                        {$subtitle = str_replace('Cartus cerneala', 'Accesorii ', $category.name)}
                    {else}
                        {if $category.id_parent == 16323}
                            {$subtitle = $key}
                        {else}
                            {$subtitle = $category.name}
                        {/if}
                    {/if}
                {/if}
                {if $category.id == Tools::getImprimanteCategoryId() && $key != 'uncategorized'} {* Imprimante *}
                    {$subtitle = $key}
                {/if}
                {if $groups_with_products > 1}
                    <h2 class="h2 h2-bordered category-subtitle category-subtitle-{$key}" data-id_category="{$category.id}">{$subtitle}</h2>
                {/if}
                <div class="products products-grouped">
                    {foreach from=$group item="product"}
                        {block name='product_miniature'}
                            {include file='catalog/_partials/miniatures/product_grouped.tpl' product=$product}
                        {/block}
                    {/foreach}
                </div>
            {/if}
        {/foreach}
    {elseif isset($page) && $page.page_name == 'search'}
        <div class="products products-grouped">
            {foreach from=$listing.products item="product"}
                {block name='product_miniature'}
                    {include file='catalog/_partials/miniatures/product_grouped.tpl' product=$product}
                {/block}
            {/foreach}
            {if isset($listing.pagination.search_categories_found) && $listing.pagination.search_categories_found}
                {foreach from=$listing.pagination.search_categories_found item=cat}
                    {block name='product_miniature'}
                        {include file='catalog/_partials/miniatures/category_grouped.tpl' cat=$cat}
                    {/block}
                {/foreach}
            {/if}
        </div>
    {else}
        <div class="products products-nongrouped">
          {foreach from=$listing.products item="product"}
              {block name='product_miniature'}
                  {include file='catalog/_partials/miniatures/product.tpl' product=$product}
              {/block}
          {/foreach}
        </div>
    {/if}
  {*if !isset($category)*}
    {block name='pagination'}
      {include file='_partials/pagination.tpl' pagination=$listing.pagination}
    {/block}
  {*/if*}
  <div class="hidden-md-up text-xs-right up">
    <a href="#header" class="btn btn-secondary">
      {l s='Back to top' d='Shop.Theme.Actions'}
      <i class="material-icons">&#xE316;</i>
    </a>
  </div>
</div>
