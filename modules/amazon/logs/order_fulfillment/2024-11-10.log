00:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

00:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

00:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

00:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

00:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

00:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

01:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

01:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

01:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

01:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

01:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

01:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

02:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

02:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

02:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

02:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

02:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

02:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

03:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

03:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

03:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

03:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

03:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

03:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

04:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

04:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

04:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

04:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

04:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

04:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

05:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

05:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

05:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

05:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

05:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

05:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

06:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

06:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

06:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

06:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

06:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

06:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

07:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

07:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

07:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

07:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

07:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

07:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

08:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

08:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

08:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

08:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

08:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

08:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

09:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

09:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

09:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

09:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

09:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

09:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

10:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

10:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

10:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

10:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

10:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

10:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

11:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

11:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

11:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

11:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

11:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

11:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

12:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

12:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

12:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

12:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

12:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

12:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

13:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

13:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

13:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

13:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

13:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

13:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

14:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

14:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

14:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

14:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

14:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

14:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

15:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

15:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

15:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

15:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

15:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

15:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

16:24:03 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

16:24:03 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

16:24:03 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

16:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

16:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

16:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

17:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

17:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

17:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

17:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

17:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

17:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

18:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

18:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

18:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

18:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

18:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

18:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

19:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

19:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

19:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

19:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

19:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

19:54:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

20:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

20:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

20:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

20:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

20:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

20:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

21:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

21:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

21:24:02 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

21:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

21:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

21:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

22:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

22:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

22:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

22:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

22:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

22:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

23:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

23:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

23:24:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

23:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#85): . Updating order statuses for: Amazon APJ6JRA9NG5V4. Current time zone: Europe/Bucharest. - Period: 15 days. . - State: Expediat amazon (20). . </pre>

23:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#91): . No Orders - exiting normally. </pre>

23:54:01 > DEBUG > <pre>status.php(#264) -> status.php(#92) -> status.php(#259): . --------------------------------------------------------------------------------. </pre>

