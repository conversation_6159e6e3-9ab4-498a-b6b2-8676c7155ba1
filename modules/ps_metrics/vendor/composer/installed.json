{"packages": [{"name": "clue/stream-filter", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "time": "2023-12-20T15:40:13+00:00", "default-branch": true, "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"ps_metrics_module_v4_0_5\\Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "install-path": "../clue/stream-filter"}, {"name": "composer/pcre", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "437d09fdc9fbce60cb9defb28473e864b33c2d28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/437d09fdc9fbce60cb9defb28473e864b33c2d28", "reference": "437d09fdc9fbce60cb9defb28473e864b33c2d28", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5"}, "time": "2022-01-21T20:27:39+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/main"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./pcre"}, {"name": "composer/semver", "version": "dev-main", "version_normalized": "dev-main", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "1d09200268e7d1052ded8e5da9c73c96a63d18f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/1d09200268e7d1052ded8e5da9c73c96a63d18f5", "reference": "1d09200268e7d1052ded8e5da9c73c96a63d18f5", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "time": "2023-08-31T12:20:31+00:00", "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/main"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./semver"}, {"name": "composer/xdebug-handler", "version": "2.0.x-dev", "version_normalized": "2.0.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/9e36aeed4616366d2b690bdce11f71e9178c579a", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a", "shasum": ""}, "require": {"composer/pcre": "^1", "php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5.0 || ^6.0"}, "time": "2022-02-24T20:20:32+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./xdebug-handler"}, {"name": "doctrine/annotations", "version": "1.14.x-dev", "version_normalized": "1.14.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "50f9235edd3a3b0fc509d458eaf469f2f5cad932"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/50f9235edd3a3b0fc509d458eaf469f2f5cad932", "reference": "50f9235edd3a3b0fc509d458eaf469f2f5cad932", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "~1.4.10 || ^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "time": "2023-08-14T13:42:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.x"}, "install-path": "../doctrine/annotations"}, {"name": "doctrine/deprecations", "version": "1.1.x-dev", "version_normalized": "1.1.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "time": "2024-01-30T19:34:25+00:00", "default-branch": true, "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "install-path": "../doctrine/deprecations"}, {"name": "doctrine/lexer", "version": "2.1.x-dev", "version_normalized": "2.1.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "time": "2024-02-05T11:35:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "install-path": "../doctrine/lexer"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/PHP-CS-Fixer.git", "reference": "47177af1cfb9dab5d1cc4daf91b7179c2efe7fad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/PHP-CS-Fixer/zipball/47177af1cfb9dab5d1cc4daf91b7179c2efe7fad", "reference": "47177af1cfb9dab5d1cc4daf91b7179c2efe7fad", "shasum": ""}, "require": {"composer/semver": "^3.2", "composer/xdebug-handler": "^2.0", "doctrine/annotations": "^1.12", "ext-json": "*", "ext-tokenizer": "*", "php": "^7.2.5 || ^8.0", "php-cs-fixer/diff": "^2.0", "symfony/console": "^4.4.20 || ^5.1.3 || ^6.0", "symfony/event-dispatcher": "^4.4.20 || ^5.0 || ^6.0", "symfony/filesystem": "^4.4.20 || ^5.0 || ^6.0", "symfony/finder": "^4.4.20 || ^5.0 || ^6.0", "symfony/options-resolver": "^4.4.20 || ^5.0 || ^6.0", "symfony/polyfill-mbstring": "^1.23", "symfony/polyfill-php80": "^1.23", "symfony/polyfill-php81": "^1.23", "symfony/process": "^4.4.20 || ^5.0 || ^6.0", "symfony/stopwatch": "^4.4.20 || ^5.0 || ^6.0"}, "require-dev": {"justinrainbow/json-schema": "^5.2", "keradus/cli-executor": "^1.5", "mikey179/vfsstream": "^1.6.8", "php-coveralls/php-coveralls": "^2.5.2", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.2", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.2.1", "phpspec/prophecy": "^1.15", "phpspec/prophecy-phpunit": "^1.1 || ^2.0", "phpunit/phpunit": "^8.5.21 || ^9.5", "phpunitgoodpractices/polyfill": "^1.5", "phpunitgoodpractices/traits": "^1.9.1", "symfony/phpunit-bridge": "^5.2.4 || ^6.0", "symfony/yaml": "^4.4.20 || ^5.0 || ^6.0"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "time": "2021-12-11T16:25:08+00:00", "bin": ["php-cs-fixer"], "type": "application", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\PhpCsFixer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "support": {"issues": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/issues", "source": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/tree/v3.4.0"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "install-path": "../friendsofphp/php-cs-fixer"}, {"name": "guzzlehttp/psr7", "version": "1.9.x-dev", "version_normalized": "1.9.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e4490cabc77465aaee90b20cfc9a770f8c04be6b", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2023-04-17T16:00:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "nikic/php-parser", "version": "4.x-dev", "version_normalized": "4.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "4d36e9c16f4820c2ed9360bc818982f3c02a08f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/4d36e9c16f4820c2ed9360bc818982f3c02a08f5", "reference": "4d36e9c16f4820c2ed9360bc818982f3c02a08f5", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "time": "2024-03-17T09:03:35+00:00", "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/4.x"}, "install-path": "../nikic/php-parser"}, {"name": "php-cs-fixer/diff", "version": "v2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/diff.git", "reference": "29dc0d507e838c4580d018bd8b5cb412474f7ec3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/diff/zipball/29dc0d507e838c4580d018bd8b5cb412474f7ec3", "reference": "29dc0d507e838c4580d018bd8b5cb412474f7ec3", "shasum": ""}, "require": {"php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7.23 || ^6.4.3 || ^7.0", "symfony/process": "^3.3"}, "time": "2020-10-14T08:32:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "sebastian/diff v3 backport support for PHP 5.6+", "homepage": "https://github.com/PHP-CS-Fixer", "keywords": ["diff"], "support": {"issues": "https://github.com/PHP-CS-Fixer/diff/issues", "source": "https://github.com/PHP-CS-Fixer/diff/tree/v2.0.2"}, "abandoned": true, "install-path": "../php-cs-fixer/diff"}, {"name": "php-http/httplug", "version": "v1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "1c6381726c18579c4ca2ef1ec1498fdae8bdf018"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/1c6381726c18579c4ca2ef1ec1498fdae8bdf018", "reference": "1c6381726c18579c4ca2ef1ec1498fdae8bdf018", "shasum": ""}, "require": {"php": ">=5.4", "php-http/promise": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "^1.0", "phpspec/phpspec": "^2.4"}, "time": "2016-08-31T08:30:17+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/master"}, "install-path": "../php-http/httplug"}, {"name": "php-http/message", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "4cb00d6d316783d357a59ec94c234c50aca515f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/4cb00d6d316783d357a59ec94c234c50aca515f5", "reference": "4cb00d6d316783d357a59ec94c234c50aca515f5", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "time": "2024-03-16T19:07:08+00:00", "default-branch": true, "type": "library", "installation-source": "dist", "autoload": {"files": ["src/filters.php"], "psr-4": {"ps_metrics_module_v4_0_5\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.x"}, "install-path": "../php-http/message"}, {"name": "php-http/promise", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "time": "2024-03-15T13:55:21+00:00", "default-branch": true, "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "install-path": "../php-http/promise"}, {"name": "phpoption/phpoption", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "416ca2ac2a84555b785a98002d613fe13d1d1c2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/416ca2ac2a84555b785a98002d613fe13d1d1c2f", "reference": "416ca2ac2a84555b785a98002d613fe13d1d1c2f", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "time": "2023-11-12T22:52:20+00:00", "default-branch": true, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/master"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "install-path": "../phpoption/phpoption"}, {"name": "phpstan/phpstan", "version": "1.11.x-dev", "version_normalized": "1.11.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "d01880549254a0888ef48e643beb0cba87b16a17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/d01880549254a0888ef48e643beb0cba87b16a17", "reference": "d01880549254a0888ef48e643beb0cba87b16a17", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "time": "2024-04-09T14:22:37+00:00", "default-branch": true, "bin": ["phpstan", "phpstan.phar"], "type": "library", "installation-source": "dist", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "install-path": "../phpstan/phpstan"}, {"name": "prestashop/autoindex", "version": "v2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PrestaShopCorp/autoindex.git", "reference": "235f3ec115432ffc32d582198ea498467b3946d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShopCorp/autoindex/zipball/235f3ec115432ffc32d582198ea498467b3946d0", "reference": "235f3ec115432ffc32d582198ea498467b3946d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.10", "php": "^8.0 || ^7.2", "symfony/console": "^3.4 || ~4.0 || ~5.0 || ~6.0", "symfony/finder": "^3.4 || ~4.0 || ~5.0 || ~6.0"}, "require-dev": {"phpstan/phpstan": "^0.12.83", "prestashop/php-dev-tools": "1.*"}, "time": "2022-10-10T08:35:00+00:00", "bin": ["bin/autoindex"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\PrestaShop\\AutoIndex\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "Automatically add an 'index.php' in all the current or specified directories and all sub-directories.", "homepage": "https://github.com/PrestaShopCorp/autoindex", "support": {"source": "https://github.com/PrestaShopCorp/autoindex/tree/v2.1.0"}, "install-path": "../prestashop/autoindex"}, {"name": "prestashop/header-stamp", "version": "v2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PrestaShopCorp/header-stamp.git", "reference": "3104b69ad73b6039c7082dbba4af9dbeb0b936b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShopCorp/header-stamp/zipball/3104b69ad73b6039c7082dbba4af9dbeb0b936b3", "reference": "3104b69ad73b6039c7082dbba4af9dbeb0b936b3", "shasum": ""}, "require": {"nikic/php-parser": "^4.10", "php": "^8.0 || ^7.2", "symfony/console": "^3.4 || ~4.0 || ~5.0 || ~6.0", "symfony/finder": "^3.4 || ~4.0 || ~5.0 || ~6.0"}, "require-dev": {"phpstan/phpstan": "^0.12.83", "prestashop/php-dev-tools": "1.*"}, "time": "2023-03-23T14:44:10+00:00", "bin": ["bin/header-stamp"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\PrestaShop\\HeaderStamp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "Rewrite your file headers to add the license or to make them up-to-date", "homepage": "https://github.com/PrestaShopCorp/header-stamp", "support": {"issues": "https://github.com/PrestaShopCorp/header-stamp/issues", "source": "https://github.com/PrestaShopCorp/header-stamp/tree/v2.3"}, "install-path": "../prestashop/header-stamp"}, {"name": "prestashop/module-lib-cache-directory-provider", "version": "v1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PrestaShopCorp/module-lib-cache-directory-provider.git", "reference": "34a577b66a7e52ae16d6f40efd1db17290bad453"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShopCorp/module-lib-cache-directory-provider/zipball/34a577b66a7e52ae16d6f40efd1db17290bad453", "reference": "34a577b66a7e52ae16d6f40efd1db17290bad453", "shasum": ""}, "require": {"php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "~5.7"}, "time": "2020-09-08T14:13:23+00:00", "type": "project", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\PrestaShop\\ModuleLibCacheDirectoryProvider\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "Cache directory provider to use on prestashop modules", "keywords": ["composer", "modules", "package", "prestashop"], "support": {"issues": "https://github.com/PrestaShopCorp/module-lib-cache-directory-provider/issues", "source": "https://github.com/PrestaShopCorp/module-lib-cache-directory-provider/tree/master"}, "install-path": "../prestashop/module-lib-cache-directory-provider"}, {"name": "prestashop/module-lib-guzzle-adapter", "version": "v0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PrestaShopCorp/module-lib-guzzle-adapter.git", "reference": "451477b899b6fae8865a0face5b3362b07a1f947"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShopCorp/module-lib-guzzle-adapter/zipball/451477b899b6fae8865a0face5b3362b07a1f947", "reference": "451477b899b6fae8865a0face5b3362b07a1f947", "shasum": ""}, "require": {"guzzlehttp/psr7": "~1.9", "php": ">=5.6.0", "php-http/httplug": "~1.1", "php-http/message": "^1.7", "psr/http-message": "^1.0"}, "provide": {"php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"phpstan/phpstan": "^1.7", "phpunit/phpunit": "^9.5|^8.5|^5.7", "prestashop/php-dev-tools": "^4.2|^3.16"}, "time": "2023-01-11T15:09:56+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Prestashop\\ModuleLibGuzzleAdapter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "Plug modules to the Guzzle client available on a running shop", "support": {"issues": "https://github.com/PrestaShopCorp/module-lib-guzzle-adapter/issues", "source": "https://github.com/PrestaShopCorp/module-lib-guzzle-adapter/tree/v0.6"}, "install-path": "../prestashop/module-lib-guzzle-adapter"}, {"name": "prestashop/module-lib-mbo-installer", "version": "v0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PrestaShopCorp/module-lib-mbo-installer.git", "reference": "c9e598307ef17975e10c98c98538c2d3512b962b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShopCorp/module-lib-mbo-installer/zipball/c9e598307ef17975e10c98c98538c2d3512b962b", "reference": "c9e598307ef17975e10c98c98538c2d3512b962b", "shasum": ""}, "require": {"php": ">=5.6", "prestashop/module-lib-guzzle-adapter": "^0.6"}, "require-dev": {"phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5|^8.5|^5.7", "prestashop/php-dev-tools": "^4.2|^3.16"}, "time": "2023-02-06T10:42:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Prestashop\\ModuleLibMboInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A helper to ease the download PS MBO from the Addons Marketplace", "support": {"issues": "https://github.com/PrestaShopCorp/module-lib-mbo-installer/issues", "source": "https://github.com/PrestaShopCorp/module-lib-mbo-installer/tree/v0.1"}, "install-path": "../prestashop/module-lib-mbo-installer"}, {"name": "prestashop/module-lib-service-container", "version": "v2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PrestaShopCorp/module-lib-service-container.git", "reference": "5525b56513d9ddad6e4232dfd93a24e028efdca7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShopCorp/module-lib-service-container/zipball/5525b56513d9ddad6e4232dfd93a24e028efdca7", "reference": "5525b56513d9ddad6e4232dfd93a24e028efdca7", "shasum": ""}, "require": {"php": ">=5.6.0", "prestashop/module-lib-cache-directory-provider": "^1.0"}, "require-dev": {"phpunit/phpunit": "~5.7"}, "suggest": {"symfony/config": "Needed when the running PrestaShop does not already run with Symfony", "symfony/dependency-injection": "Needed when the running PrestaShop does not already run with Symfony", "symfony/expression-language": "Needed when the running PrestaShop does not already run with Symfony", "symfony/yaml": "Needed when the running PrestaShop does not already run with Symfony"}, "time": "2022-06-20T08:30:47+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PrestaShop\\ModuleLibServiceContainer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "Service container to use on prestashop modules", "keywords": ["composer", "modules", "package", "prestashop"], "support": {"issues": "https://github.com/PrestaShopCorp/module-lib-service-container/issues", "source": "https://github.com/PrestaShopCorp/module-lib-service-container/tree/v2.0"}, "install-path": "../prestashop/module-lib-service-container"}, {"name": "prestashop/php-dev-tools", "version": "v4.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PrestaShop/php-dev-tools.git", "reference": "843275b19729ba810d8ba2b9c97b568e5bbabe03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/php-dev-tools/zipball/843275b19729ba810d8ba2b9c97b568e5bbabe03", "reference": "843275b19729ba810d8ba2b9c97b568e5bbabe03", "shasum": ""}, "require": {"friendsofphp/php-cs-fixer": "^3.2", "php": ">=7.2.5", "prestashop/autoindex": "^2.0", "prestashop/header-stamp": "^2.0", "squizlabs/php_codesniffer": "^3.4", "symfony/console": "~3.2 || ~4.0 || ~5.0 || ~6.0", "symfony/filesystem": "~3.2 || ~4.0 || ~5.0 || ~6.0"}, "time": "2022-10-18T14:19:51+00:00", "bin": ["bin/prestashop-coding-standards"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\PrestaShop\\CodingStandards\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PrestaShop coding standards", "support": {"issues": "https://github.com/PrestaShop/php-dev-tools/issues", "source": "https://github.com/PrestaShop/php-dev-tools/tree/v4.3.0"}, "install-path": "../prestashop/php-dev-tools"}, {"name": "prestashop/prestashop-accounts-installer", "version": "v1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PrestaShopCorp/prestashop-accounts-installer.git", "reference": "f038af2408968d1045330b32aa1fed65fcaf4c9b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShopCorp/prestashop-accounts-installer/zipball/f038af2408968d1045330b32aa1fed65fcaf4c9b", "reference": "f038af2408968d1045330b32aa1fed65fcaf4c9b", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "fzaninotto/faker": "^1.9", "phpunit/phpunit": "^5.7", "prestashop/php-dev-tools": "3.*"}, "time": "2021-07-23T15:40:36+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\PrestaShop\\PsAccountsInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Utility package to install `ps_accounts` module or present data to trigger manual install from psx configuration page.", "support": {"issues": "https://github.com/PrestaShopCorp/prestashop-accounts-installer/issues", "source": "https://github.com/PrestaShopCorp/prestashop-accounts-installer/tree/v1.0.1"}, "install-path": "../prestashop/prestashop-accounts-installer"}, {"name": "psr/cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T20:24:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "install-path": "../psr/cache"}, {"name": "psr/container", "version": "1.1.x-dev", "version_normalized": "1.1.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2021-03-05T17:36:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.x"}, "install-path": "../psr/container"}, {"name": "psr/event-dispatcher", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "bbd9eacc080d33861e5b5c75b3b8c4d7e6d01874"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/bbd9eacc080d33861e5b5c75b3b8c4d7e6d01874", "reference": "bbd9eacc080d33861e5b5c75b3b8c4d7e6d01874", "shasum": ""}, "require": {"php": ">=7.2.0"}, "suggest": {"fig/event-dispatcher-util": "Provides some useful PSR-14 utilities"}, "time": "2024-03-17T21:29:03+00:00", "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"source": "https://github.com/php-fig/event-dispatcher"}, "install-path": "../psr/event-dispatcher"}, {"name": "psr/http-message", "version": "1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:50:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "segmentio/analytics-php", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/segmentio/analytics-php.git", "reference": "403374d02818ecc6f9a5374a4dc88d913cba6021"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/segmentio/analytics-php/zipball/403374d02818ecc6f9a5374a4dc88d913cba6021", "reference": "403374d02818ecc6f9a5374a4dc88d913cba6021", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "time": "2014-06-17T00:28:51+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["lib/Segment.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Segment.io <<EMAIL>>", "homepage": "https://segment.io/"}], "description": "Segmentio Analytics PHP Library", "homepage": "https://segment.io/libraries/php", "keywords": ["analytics", "analytics.js", "segmentio"], "support": {"issues": "https://github.com/segmentio/analytics-php/issues", "source": "https://github.com/segmentio/analytics-php/tree/v1"}, "install-path": "../segmentio/analytics-php"}, {"name": "squizlabs/php_codesniffer", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "2d4783cb2977bd616442453ad280ce253ea658b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/2d4783cb2977bd616442453ad280ce253ea658b1", "reference": "2d4783cb2977bd616442453ad280ce253ea658b1", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "time": "2024-04-09T13:49:12+00:00", "default-branch": true, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "install-path": "../squizlabs/php_codesniffer"}, {"name": "symfony/console", "version": "5.4.x-dev", "version_normalized": "5.4.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "2bc6c8d898ecff748440ca24ffc4921a66b2d52a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/2bc6c8d898ecff748440ca24ffc4921a66b2d52a", "reference": "2bc6c8d898ecff748440ca24ffc4921a66b2d52a", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "time": "2024-04-03T14:00:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/console"}, {"name": "symfony/deprecation-contracts", "version": "2.5.x-dev", "version_normalized": "2.5.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "80d075412b557d41002320b96a096ca65aa2c98d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/80d075412b557d41002320b96a096ca65aa2c98d", "reference": "80d075412b557d41002320b96a096ca65aa2c98d", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2023-01-24T14:02:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/event-dispatcher", "version": "5.4.x-dev", "version_normalized": "5.4.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "7a69a85c7ea5bdd1e875806a99c51a87d3a74b38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/7a69a85c7ea5bdd1e875806a99c51a87d3a74b38", "reference": "7a69a85c7ea5bdd1e875806a99c51a87d3a74b38", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "time": "2024-01-23T13:51:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher"}, {"name": "symfony/event-dispatcher-contracts", "version": "2.5.x-dev", "version_normalized": "2.5.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "540f4c73e87fd0c71ca44a6aa305d024ac68cb73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/540f4c73e87fd0c71ca44a6aa305d024ac68cb73", "reference": "540f4c73e87fd0c71ca44a6aa305d024ac68cb73", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "time": "2024-01-23T13:51:25+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher-contracts"}, {"name": "symfony/filesystem", "version": "5.4.x-dev", "version_normalized": "5.4.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "db9c7b15bdc4c1fba8b4cd038c0d81dea6596359"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/db9c7b15bdc4c1fba8b4cd038c0d81dea6596359", "reference": "db9c7b15bdc4c1fba8b4cd038c0d81dea6596359", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/process": "^5.4|^6.4"}, "time": "2024-04-04T09:19:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/filesystem"}, {"name": "symfony/finder", "version": "5.4.x-dev", "version_normalized": "5.4.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "abe6d6f77d9465fed3cd2d029b29d03b56b56435"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/abe6d6f77d9465fed3cd2d029b29d03b56b56435", "reference": "abe6d6f77d9465fed3cd2d029b29d03b56b56435", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "time": "2024-01-23T13:51:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/finder"}, {"name": "symfony/options-resolver", "version": "5.4.x-dev", "version_normalized": "5.4.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "4fe5cf6ede71096839f0e4b4444d65dd3a7c1eb9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/4fe5cf6ede71096839f0e4b4444d65dd3a7c1eb9", "reference": "4fe5cf6ede71096839f0e4b4444d65dd3a7c1eb9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "time": "2023-02-14T08:03:56+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/options-resolver"}, {"name": "symfony/polyfill-ctype", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ef4d7e442ca910c4764bce785146269b30cb5fc4", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2024-01-29T20:11:03+00:00", "default-branch": true, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-intl-grapheme", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/32a9da87d7b3245e09ac426c83d334ae9f06f80f", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-01-29T20:11:03+00:00", "default-branch": true, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-grapheme"}, {"name": "symfony/polyfill-intl-normalizer", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/bc45c394692b948b4d383a08d7753968bed9a83d", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-01-29T20:11:03+00:00", "default-branch": true, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "e5e7ddb00b859dbdf5ad8f3bbe4cd29a3a37aa34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/e5e7ddb00b859dbdf5ad8f3bbe4cd29a3a37aa34", "reference": "e5e7ddb00b859dbdf5ad8f3bbe4cd29a3a37aa34", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-03-14T13:49:05+00:00", "default-branch": true, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/1.x"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php73", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "21bd091060673a1177ae842c0ef8fe30893114d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/21bd091060673a1177ae842c0ef8fe30893114d2", "reference": "21bd091060673a1177ae842c0ef8fe30893114d2", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2024-01-29T20:11:03+00:00", "default-branch": true, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php73"}, {"name": "symfony/polyfill-php80", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2024-01-29T20:11:03+00:00", "default-branch": true, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php80"}, {"name": "symfony/polyfill-php81", "version": "1.x-dev", "version_normalized": "1.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "c565ad1e63f30e7477fc40738343c62b40bc672d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/c565ad1e63f30e7477fc40738343c62b40bc672d", "reference": "c565ad1e63f30e7477fc40738343c62b40bc672d", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2024-01-29T20:11:03+00:00", "default-branch": true, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php81"}, {"name": "symfony/process", "version": "5.4.x-dev", "version_normalized": "5.4.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "4fdf34004f149cc20b2f51d7d119aa500caad975"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/4fdf34004f149cc20b2f51d7d119aa500caad975", "reference": "4fdf34004f149cc20b2f51d7d119aa500caad975", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "time": "2024-02-12T15:49:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/process"}, {"name": "symfony/service-contracts", "version": "2.5.x-dev", "version_normalized": "2.5.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/a2329596ddc8fd568900e3fc76cba42489ecc7f3", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "time": "2023-04-21T15:04:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/service-contracts"}, {"name": "symfony/stopwatch", "version": "5.4.x-dev", "version_normalized": "5.4.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "887762aa99ff16f65dc8b48aafead415f942d407"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/887762aa99ff16f65dc8b48aafead415f942d407", "reference": "887762aa99ff16f65dc8b48aafead415f942d407", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/service-contracts": "^1|^2|^3"}, "time": "2024-01-23T13:51:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/stopwatch"}, {"name": "symfony/string", "version": "5.4.x-dev", "version_normalized": "5.4.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "4e232c83622bd8cd32b794216aa29d0d266d353b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/4e232c83622bd8cd32b794216aa29d0d266d353b", "reference": "4e232c83622bd8cd32b794216aa29d0d266d353b", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "time": "2024-02-01T08:49:30+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/string"}, {"name": "vlucas/phpdotenv", "version": "3.6.x-dev", "version_normalized": "3.6.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "5b547cdb25825f10251370f57ba5d9d924e6f68e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/5b547cdb25825f10251370f57ba5d9d924e6f68e", "reference": "5b547cdb25825f10251370f57ba5d9d924e6f68e", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.5.2", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.21"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "time": "2021-12-12T23:02:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"ps_metrics_module_v4_0_5\\Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/3.6"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "install-path": "../vlucas/phpdotenv"}], "dev": true, "dev-package-names": ["composer/pcre", "composer/semver", "composer/xdebug-handler", "doctrine/annotations", "doctrine/deprecations", "doctrine/lexer", "friendsofphp/php-cs-fixer", "nikic/php-parser", "php-cs-fixer/diff", "phpstan/phpstan", "prestashop/autoindex", "prestashop/header-stamp", "prestashop/php-dev-tools", "psr/cache", "psr/container", "psr/event-dispatcher", "psr/log", "squizlabs/php_codesniffer", "symfony/console", "symfony/deprecation-contracts", "symfony/event-dispatcher", "symfony/event-dispatcher-contracts", "symfony/filesystem", "symfony/finder", "symfony/options-resolver", "symfony/polyfill-intl-grapheme", "symfony/polyfill-intl-normalizer", "symfony/polyfill-mbstring", "symfony/polyfill-php73", "symfony/polyfill-php80", "symfony/polyfill-php81", "symfony/process", "symfony/service-contracts", "symfony/stopwatch", "symfony/string"]}