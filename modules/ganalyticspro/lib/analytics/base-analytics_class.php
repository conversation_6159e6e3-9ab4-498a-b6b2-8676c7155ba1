<?php
 /**
 * Google Analyics Pro
 *
 * <AUTHOR> - https://www.businesstech.fr
 * @copyright Business Tech 2020 - https://www.businesstech.fr
 * @license   Commercial
 *
 *           ____    _______
 *          |  _ \  |__   __|
 *          | |_) |    | |
 *          |  _ <     | |
 *          | |_) |    | |
 *          |____/     |_|
 */

abstract class BT_GapAnalyticsBase
{
    /**
     * @var string $sJsOutput : store the output JS
     */
    protected $sJsOutput = '';

    /**
     * @var array $aApiOutput : store the output data
     */
    protected $aApiOutput = '';

    /**
     * @var bool $bBackOffice : define if we are in BO context
     */
    public $bBackOffice = false;


    /**
     * Magic Method __construct
     *
     * @param array $aParams
     */
    abstract public function __construct(array $aParams = null);

    /**
     * init() method defines which options are activated or not
     *
     * @param array $aParams
     */
    abstract public function init(array $aParams = null);


    /**
     * output() method send the output
     *
     * @param mixed $mParam
     */
    abstract public function output($mParam = true);


    /**
     * getAffiliation() method set the affiliation
     *
     * @param string $sSource
     * @param string $sDefault
     * @return string
     */
    public function getAffiliation($sSource, $sDefault)
    {
        return (
        !empty($sSource) ? $sSource : $sDefault
        );
    }


    /**
     * getGaJS() method returns the JS GA call
     *
     * @return string
     */
    public function getGaJS()
    {
        return '(function(i,s,o,g,r,a,m){
			i[\'GoogleAnalyticsObject\']=r;
			i[r]=i[r]||function(){
				(i[r].q=i[r].q||[]).push(arguments)
			}, i[r].l=1*new Date();
			a=s.createElement(o), m=s.getElementsByTagName(o)[0];
			a.async=1;
			a.src=g;
			m.parentNode.insertBefore(a,m)
		})(window,document,\'script\',\'//www.google-analytics.com/analytics.js\',\'ga\');';
    }


    /**
     * isActive() method returns if the current object is active
     *
     * @return string
     */
    public function isActive()
    {
        return $this->active;
    }

    /**
     * deactivate() method deactivate the analytics object => useful in the back context when nothing happen
     */
    public function deactivate()
    {
        $this->active = false;
    }


    /**
     * concatenateJS() method concatenate JS instructions
     *
     * @param string $sJsInstruction
     * @param bool $bReset
     * @return string
     */
    public function concatenateJS($sJsInstruction, $bReset = false)
    {
        if ($bReset) {
            $this->reset();
        }

        $this->sJsOutput .= $sJsInstruction . "\n";
    }

    /**
     * reset() method reset the output JS string
     *
     */
    public function reset()
    {
        $this->sJsOutput = '';
        $this->aApiOutput = array();
    }


    /**
     * getVoucher() method return the name of the voucher
     *
     * @param obj $oOrder
     * @return string
     */
    public function getVoucher($oOrder)
    {
        // set
        $sVoucherName = '';

        if (Validate::isLoadedObject($oOrder)) {
            // Get Discounts applied in the order
            $aVouchers = $oOrder->getCartRules();

            if (!empty($aVouchers)) {
                foreach ($aVouchers as $i => $aVoucher) {
                    $sVoucherName .= $aVoucher['name'] . (!empty($aVouchers[$i + 1]) ? '/' : '');
                }
            }
        }

        return $sVoucherName;
    }

    /**
     * getCombination() method return data about product attribute id
     *
     * @param int $iProductId
     * @param int $iAttributeId
     * @param int $iCustomerId
     * @return array
     */
    public function getCombination($iProductId, $iAttributeId, $iCustomerId = null)
    {
        // set vars
        $aCombination = array();
        $sCombinationName = '';

        // get attributes
        $aAttributes = Product::getAttributesParams($iProductId, $iAttributeId);

        if (!empty($aAttributes)) {
            $iTotal = count($aAttributes);
            $index = 1;

            foreach ($aAttributes as $aAttr) {
                $sCombinationName .= $aAttr['group'] . ' ' . $aAttr['name'] . (($index++ == $iTotal) ? '' : ', ');
            }
            $aCombination['name'] = $sCombinationName;
            $aCombination['price'] = BT_GapModuleTools::getProductPrice($iProductId, $iAttributeId, $iCustomerId);

            unset($sCombinationName);
            unset($index);
            unset($iTotal);
        }

        return $aCombination;
    }


    /**
     * wrapProducts() method format products and returned them
     *
     * @param array $aProducts
     * @param string $sMethod
     * @param int $iPosition
     * @param array $aExtraParams
     * @return array
     */
    protected function wrapProducts(array $aProducts, $sMethod, $iPosition = 0, array $aExtraParams = array())
    {
        $aFormattedProducts = array();

        if (!empty($aProducts)) {
            foreach ($aProducts as $index => $aProduct) {
                if (empty(GAnalyticsPro::$bCompare1750)) {
                    if ($aProduct instanceof Product) {
                        $aProduct = (array)$aProduct;
                    }
                } else {
                    $aProduct['id'] = $aProduct['id_product'];
                }
                // populate the output array
                $aFormattedProducts[] = $this->{$sMethod}($aProduct, $iPosition, $aExtraParams);

                if ($iPosition !== 0) {
                    ++$iPosition;
                }
            }
        }

        return $aFormattedProducts;
    }


    /**
     * wrapProduct() method format product and returned it
     *
     * @param array $aProduct
     * @param int $iPosition
     * @param array $aExtraParams
     * @return array
     */
    public function wrapProduct(array $aProduct, $iPosition, array $aExtraParams = array())
    {
        // set variable
        $aGAProduct = array();

        // get the customer ID passed as arg or the current one
        $iCustomerId = !empty($aExtraParams['iCustomerId']) ? $aExtraParams['iCustomerId'] : Context::getContext()->customer->id;

        if (empty(GAnalyticsPro::$bCompare1750)) {
            $aGAProduct = array(
                'id' => (!empty($aProduct['id_product']) ? $aProduct['id_product'] : $aProduct['id']),
            );
        } // Use case for PS *******
        else {
            $aGAProduct = array(
                'id' => (!empty($aProduct['id']) ? $aProduct['id'] : ''),
            );
        }

        // name or id are mandatory
        if (!empty($aProduct['product_name'])
            || !empty($aProduct['name'])
        ) {
            $aGAProduct['name'] = addslashes(((!empty($aProduct['product_name']) ? $aProduct['product_name'] : $aProduct['name'])));
        }
        // category optional
        if (!empty($aProduct['id_category'])
            || !empty($aProduct['category'])
        ) {
            $aGAProduct['category'] = (!empty($aProduct['id_category']) ? BT_GapModuleTools::getPath($aProduct['id_category'],
                GAnalyticsPro::$iCurrentLang, '/', false) : $aProduct['category']);
        }
        // brand optional
        if (!empty($aProduct['manufacturer_name'])) {
            $aGAProduct['brand'] = addslashes($aProduct['manufacturer_name']);
        }
        // position optional
        if (!empty($iPosition)) {
            $aGAProduct['position'] = $iPosition;
        }
        // price optional
        if (!empty($aProduct['product_price_wt'])
            || !empty($aProduct['price'])
            || !empty($aProduct['price_tax_exc'])
        ) {
            $aGAProduct['price'] = BT_GapModuleTools::getProductPrice($aGAProduct['id'], null, $iCustomerId);
        }
        // qty optional
        if (!empty($aProduct['quantity'])) {
            $aGAProduct['quantity'] = (int)$aProduct['quantity'];
        }

        if (!empty($aProduct['id_product_attribute'])) {
            $aVariant = $this->getCombination($aGAProduct['id'], $aProduct['id_product_attribute'], $iCustomerId);

            if (!empty($aVariant)) {
                $aGAProduct['variant'] = addslashes($aVariant['name']);
                $aGAProduct['price'] = $aVariant['price'];
            }
        }
        unset($iCustomerId);

        return $aGAProduct;
    }


    /**
     * get() method instantiate matched model object
     *
     * @param string $sModelType
     * @param array $aParams
     * @return obj model type
     */
    public static function get($sModelType, array $aParams = null)
    {
        $sModelType = strtolower($sModelType);

        // if valid analytics class
        if (file_exists(_GAP_PATH_LIB_ANALYTICS . $sModelType . '-analytics_class.php')) {
            // require
            require_once($sModelType . '-analytics_class.php');

            // set class name
            $sClassName = 'BT_' . ucfirst($sModelType) . 'Analytics';

            try {
                $oReflection = new ReflectionClass($sClassName);

                if ($oReflection->isInstantiable()) {
                    return $oReflection->newInstance($aParams);
                } else {
                    throw new Exception(GAnalyticsPro::$oModule->l('Internal server error => analytics class isn\'t instantiable',
                        'base-ctrl_class'), 510);
                }
            } catch (ReflectionException $e) {
                throw new Exception(GAnalyticsPro::$oModule->l('Internal server error => invalid analytics class',
                    'base-ctrl_class'), 511);
            }
        } else {
            throw new Exception(GAnalyticsPro::$oModule->l('Internal server error => the analytics class file doesn\'t exist',
                'base-ctrl_class'), 512);
        }
    }
}