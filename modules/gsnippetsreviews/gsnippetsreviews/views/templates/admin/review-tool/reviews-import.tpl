{*
* 2003-2018 Business Tech
*
* <AUTHOR> Tech SARL
* @copyright  2003-2018 Business Tech SARL
*}
<script type="text/javascript">
	{literal}
	var oImportCallBack =
		[{
			//'name' : 'updatePrerequisites',
			//'url' : '{/literal}{$sURI}{literal}',
			//'params' : '{/literal}{$sCtrlParamName|escape:'htmlall':'UTF-8'}{literal}={/literal}{$sController|escape:'htmlall':'UTF-8'}{literal}&sAction=display&sType=prerequisites',
			//'toShow' : 'bt_prerequisites-settings',
			//'toHide' : 'bt_prerequisites-settings',
			//'bFancybox' : false,
			//'bFancyboxActivity' : false,
			//'sLoadbar' : null,
			//'sScrollTo' : null,
			//'oCallBack' : {}
		}];
	{/literal}
</script>

<div class="bootstrap">
	{if empty($sDisplay) || (!empty($sDisplay) && $sDisplay == 'import')}
		<h3><i class="icon icon-cloud-download"></i>&nbsp;{l s='Import your reviews' mod='gsnippetsreviews'}</h3>
		<div class="clr_10"></div>
		{if !empty($bUpdate)}
			{include file="`$sConfirmInclude`"}
			<div class="clr_10"></div>
		{elseif !empty($aErrors)}
			{include file="`$sErrorInclude`" aErrors=$aErrors}
			<div class="clr_10"></div>
		{/if}

		{* USE CASE - the default first screen *}
		{if empty($bImportFile)}
			<div class="clr_10"></div>

			<div class="form-group">
				<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
					<div class="alert alert-info">
						{l s='Technically speaking, first you need to match your CSV columns to our required review data. Some are absolutely mandatory but it\'s possible these information are missing in your CSV file, then you can select a default value in the dedicated column. But there is an important thing, if you don\'t do the matching for one of the required information by selecting a column, then you have to select a value as default from the "default values" column.' mod='gsnippetsreviews'}
						<div class="clr_5"></div>
						{l s='For example, it is possible that the review title is missing, then you should leave the matching column empty and leave the "First 30 comment chars as title" selected as default value. It\'s the same for the adding date value, and the module will use the date and time "now" as value.' mod='gsnippetsreviews'}
						<div class="clr_5"></div>
						{l s='Below, select the number of columns of the CSV file, and click on the "go matching" button. Next, you\'ll be able to do your matching, load the CSV file and check the list of available reviews that our module could import, and at last, you\'ll be able to import them (during the last action, a manual review selection is possible).' mod='gsnippetsreviews'}
					</div>
				</div>
			</div>

			<div class="clr_10"></div>

			<form class="form-horizontal col-xs-12 col-md-12 col-lg-12" action="{$sURI|escape:'htmlall':'UTF-8'}" method="post" id="bt_import-matching-form" name="bt_import-matching-form">
				<input type="hidden" name="sAction" value="{$aQueryParams.importMatching.action|escape:'htmlall':'UTF-8'}" />
				<input type="hidden" name="sType" value="{$aQueryParams.importMatching.type|escape:'htmlall':'UTF-8'}" />
				<input type="hidden" name="sDisplay" id="sImportMatchingDisplay" value="{if !empty($sDisplay)}{$sDisplay|escape:'htmlall':'UTF-8'}{else}import{/if}" />
				<div class="form-group">
					<label class="control-label col-xs-12 col-sm-12 col-md-2 col-lg-2">
						<span class="label-tooltip" data-toggle="tooltip" title="" data-original-title="{l s='Please fill the total number of columns from your CSV file, it will allow you to do the matching data efficiently.' mod='gsnippetsreviews'}">
							<strong>{l s='Fill the number of columns of the CSV reviews file' mod='gsnippetsreviews'}</strong>
						</span> :
					</label>
					<div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
						<div class="col-xs-9 col-sm-9 col-md-6 col-lg-6">
							<div class="input-group">
								<span class="input-group-addon">{l s='Total columns' mod='gsnippetsreviews'}:</span>
								<input type="text" class="input-medium" name="bt_import-nb-columns" value="" id="bt_import-nb-columns">
								<span class="input-group-addon"><i class="icon-pencil"></i></span>
							</div>
						</div>
						<span class="label-tooltip" data-toggle="tooltip" title data-original-title="{l s='Please fill the total number of columns from your CSV file, it will allow you to do the matching data efficiently.' mod='gsnippetsreviews'}">&nbsp;<span class="icon-question-sign"></span></span>
						&nbsp;
						<input type="button" name="bt_import-matching-button" value="{l s='Go matching' mod='gsnippetsreviews'}" class="btn btn-success btn-mini" onclick="oGsr.form('bt_import-matching-form', '{$sURI|escape:'htmlall':'UTF-8'}', null, 'bt_import-matching-list', 'bt_import-matching-list', false, false, null, 'import-matching', 'matching');return false;" />
					</div>
				</div>
			</form>

			<div class="clr_10"></div>

			<div id="bt_loading-div-matching" style="display: none;">
				<div class="alert alert-info">
					<p style="text-align: center !important;"><img src="{$sLoader|escape:'htmlall':'UTF-8'}" alt="Loading" /></p><div class="clr_20"></div>
					<p style="text-align: center !important;">{l s='Your reviews import matching list is in progress' mod='gsnippetsreviews'}</p>
				</div>
			</div>

			<div class="clr_10"></div>

			<div class="form-group" id="bt_import-matching-list">
			</div>

			<div class="clr_10"></div>

			<div class="clr_20"></div>
			<div class="clr_hr"></div>
			<div class="clr_20"></div>

			<div class="row">
				<div class="col-xs-12 col-sm-12 col-md-11 col-lg-11">
					<div id="bt_error-import-matching"></div>
				</div>
				<div class="col-xs-12 col-sm-12 col-md-1 col-lg-1"></div>
			</div>
		{* USE CASE - the CSV file is loaded and we list the reviews before offering the last time to cancel or import them *}
		{else}
			{* use case - we offer to reload the page and start over *}
			{if $iTotalReviewOk eq 0}
				<div class="clr_10"></div>

				<div class="form-group">
					<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
						<div class="alert alert-info">
							{l s='We couldn\'t get an available reviews list, make sure you are loading the required CSV file, and check if it\'s not empty. It could also mean you may have not done your matching well or simply there are missing information for all the reviews you\'re trying to import, or the customer or the product do not match to any of those you have in your shop. So, just click on the button below to reload the "import review page" and try to import a new valid CSV file.' mod='gsnippetsreviews'}
							<div class="clr_5"></div>
							<button  class="btn btn-default" onclick="oGsr.reloadCurrentPage(window.location.href);"><i class="icon-refresh"></i>{l s='Reload' mod='gsnippetsreviews'}</button>
						</div>
					</div>
				</div>
			{* use case - we offer to reload the page and start over *}
			{elseif $iTotalReviewOk > $iMaxPostVars}
				<div class="clr_10"></div>

				<div class="form-group">
					<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
						<div class="alert alert-warning">
							{l s='We have identified your available review list to import is greater than the number of maximum input vars that your server can post, it means the review list won\'t be processed entirely once you\'d click on the "import" button below, so contact your server administrator and ask him to increase the PHP directive "max_input_vars" to the desired number, at least the number should be greater than the total of reviews to import.' mod='gsnippetsreviews'}
							<div class="clr_5"></div>
							{l s='The number of available reviews to import:' mod='gsnippetsreviews'}
							<div class="clr_5"></div>
							{l s='The number of maximum input vars that your server can post: ' mod='gsnippetsreviews'} <strong>{$iMaxPostVars|intval}</strong>
							{l s='Just click on the button below to reload the "import review page".' mod='gsnippetsreviews'}
							<div class="clr_5"></div>
							<button  class="btn btn-default" onclick="oGsr.reloadCurrentPage(window.location.href);"><i class="icon-refresh"></i>{l s='Reload' mod='gsnippetsreviews'}</button>
						</div>
					</div>
				</div>
			{* use case - we can finalize the review import *}
			{else}
				<div class="clr_10"></div>

				<form class="form-horizontal col-xs-12 col-md-12 col-lg-12" action="{$sURI|escape:'htmlall':'UTF-8'}" method="post" id="bt_finalize-review-import-form" name="bt_finalize-review-import-form">
					<input type="hidden" name="sAction" value="{$aQueryParams.importUpdate.action|escape:'htmlall':'UTF-8'}" />
					<input type="hidden" name="sType" value="{$aQueryParams.importUpdate.type|escape:'htmlall':'UTF-8'}" />

					<div class="clr_10"></div>

					<div class="form-group">
						<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
							<div class="alert alert-info">
								{l s='We have loaded well your CSV file:' mod='gsnippetsreviews'} <strong>{$sCsvFile|escape:'htmlall':'UTF-8'}</strong>
								<div class="clr_15"></div>
								{l s='Before clicking on the "import" button at the bottom to finalize your reviews import, you can check all the reviews you really want to import, and to do so, just uncheck those you do not want to import. If everything is ok for you, then just leave all the reviews checked and click on the "import" button.' mod='gsnippetsreviews'}
							</div>
						</div>
					</div>

					<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
						<div class="btn-actions">
							<div class="btn btn-default btn-mini" id="reviewsCheck" onclick="return oGsr.selectAll('.myCheckbox', 'check');"><i class="icon-plus-square"></i>&nbsp;{l s='Check All' mod='gsnippetsreviews'}</div> - <div class="btn btn-default btn-mini" id="reviewsUnCheck" onclick="return oGsr.selectAll('.myCheckbox', 'uncheck');"><i class="icon-minus-square"></i>&nbsp;{l s='Uncheck All' mod='gsnippetsreviews'}</div>
							<div class="clr_10"></div>
						</div>
						<table class="table table-condensed table-responsive">
							<thead>
							<tr>
								<th class="col-xs-12 col-sm-12 col-md-1 col-lg-1 title_box center"><strong>{l s='Manual selection' mod='gsnippetsreviews'}</strong></th>
								<th class="col-xs-12 col-sm-12 col-md-1 col-lg-1 title_box center">{l s='Product ID' mod='gsnippetsreviews'}</th>
								<th class="col-xs-12 col-sm-12 col-md-1 col-lg-1 title_box center">{l s='Review language' mod='gsnippetsreviews'}</th>
								<th class="col-xs-12 col-sm-12 col-md-1 col-lg-1 title_box center">{l s='Product rating' mod='gsnippetsreviews'}</th>
								<th class="col-xs-12 col-sm-12 col-md-2 col-lg-2 title_box center">{l s='Customer e-mail' mod='gsnippetsreviews'}</th>
								<th class="col-xs-12 col-sm-12 col-md-2 col-lg-2 title_box center">{l s='Review title' mod='gsnippetsreviews'}</th>
								<th class="col-xs-12 col-sm-12 col-md-2 col-lg-2 title_box center">{l s='Review comment' mod='gsnippetsreviews'}</th>
								<th class="col-xs-12 col-sm-12 col-md-2 col-lg-2 title_box center">{l s='Adding date' mod='gsnippetsreviews'}</th>
							</tr>
							</thead>
							{foreach from=$aReviews.ok key=id item=aDetailOk}
							<tr class="success">
								<td class="center">
									<input type="hidden" name="bt_import-reviews[data][]" id="bt_import-reviews-data" value='{$aDetailOk.serialized|escape:'UTF-8'}' />
									<input type="checkbox" name="bt_import-reviews[checked][]" id="bt_import-reviews-checked" value="{$aDetailOk.prodId|intval}" checked="checked" class="myCheckbox" />
								</td>
								<td class="center">
									{$aDetailOk.prodId|intval}
								</td>
								<td class="center">
									{$aDetailOk.langId|intval}
								</td>
								<td class="center">
									{$aDetailOk.rating|intval}
								</td>
								<td class="center">
									{$aDetailOk.email|escape:'htmlall':'UTF-8'}
								</td>
								<td class="center">
									{$aDetailOk.reviewTitle|escape:'htmlall':'UTF-8'}
								</td>
								<td class="center">
									{$aDetailOk.reviewComment|escape:'UTF-8'}
								</td>
								<td class="center">
									{$aDetailOk.dateAdding|escape:'UTF-8'}
								</td>
							</tr>
							{/foreach}
							{if !empty($aReviews.ko)}
								{foreach from=$aReviews.ko key=id item=aDetailKo}
									<tr class="danger">
										<td class="center">
											{if !empty($aDetailKo.error)}{$aDetailKo.error|escape:'htmlall':'UTF-8'}{else}{l s='Some information are missing' mod='gsnippetsreviews'}{/if}
										</td>
										<td class="center">
											{$aDetailKo.prodId|intval}
										</td>
										<td class="center">
											{$aDetailKo.langId|intval}
										</td>
										<td class="center">
											{$aDetailKo.rating|intval}
										</td>
										<td class="center">
											{$aDetailKo.email|escape:'htmlall':'UTF-8'}
										</td>
										<td class="center">
											{$aDetailKo.reviewTitle|escape:'htmlall':'UTF-8'}
										</td>
										<td class="center">
											{$aDetailKo.reviewComment|escape:'UTF-8'}
										</td>
										<td class="center">
											{$aDetailKo.dateAdding|escape:'UTF-8'}
										</td>
									</tr>
								{/foreach}
							{/if}
						</table>
					</div>

					<div class="clr_20"></div>
					<div class="clr_hr"></div>
					<div class="clr_20"></div>

					<div class="row">
						<div class="col-xs-12 col-sm-12 col-md-11 col-lg-11">
							<div id="bt_error-import-reviews"></div>
						</div>
						<div class="col-xs-12 col-sm-12 col-md-1 col-lg-1">
							{if !empty($bImportFile) && !empty($iTotalReviewOk)}
								<button  class="btn btn-default pull-right" onclick="oGsr.form('bt_finalize-review-import-form', '{$sURI|escape:'htmlall':'UTF-8'}#3', null, 'bt_finalize-review-import-form', 'bt_finalize-review-import-form', false, false, null, 'import-reviews', 'finalize-import');return false;"><i class="process-icon-save"></i>{l s='Import' mod='gsnippetsreviews'}</button>
							{/if}
						</div>
					</div>
				</form>

				<div class="clr_10"></div>

				<div id="bt_loading-div-finalize-import" style="display: none;">
					<div class="alert alert-info">
						<p style="text-align: center !important;"><img src="{$sLoader|escape:'htmlall':'UTF-8'}" alt="Loading" /></p><div class="clr_20"></div>
						<p style="text-align: center !important;">{l s='Your reviews import is in progress' mod='gsnippetsreviews'}</p>
					</div>
				</div>
			{/if}
		{/if}

	{/if}
	<div class="clr_20"></div>
</div>

{literal}
<script type="text/javascript">
	//bootstrap components init
	$(document).ready(function() {
		$('.label-tooltip, .help-tooltip').tooltip();
	});
</script>
{/literal}