<?php
/**
 * Savvy Search Autocomplete
 *
 * @version v3.2.0 (Feb 22, 2017)
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to http://www.prestashop.com for more information.
 *
 *  <AUTHOR> Leap Lab (http://www.giantleaplab.com)
 *  @copyright 2011-2017 Giant Leap Lab
 *  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 *  International Registered Trademark & Property of PrestaShop SA
 */

/**
 * class GllFormsContainer
 */
class GllFormsContainer extends GllItemsContainer
{

    private $checkedRequest = false;
    private $keyFormRequest = false;
    private $hasErrorsRequestProcess = false;
    private $module;
    private $templateDir;
    private $context;
    private $inputsContainer;
    private $optionsContainer;
    private $keysForm;
    private $templateDirForm;
    private $templateDirInput;

    public function initProperty(GllSettingsStorage $settingsStorage)
    {
        $this->module = $settingsStorage->getModule();
        $this->context = $settingsStorage->getContext();
        $this->inputsContainer = $settingsStorage->getInputsContainer();
        $this->optionsContainer = $settingsStorage->getOptionsContainer();
        $this->keysForm = $this->getKeysEnabledItems();
        $this->templateDir = $settingsStorage->getTemplateDir();
        $this->templateDirForm = GllHCommon::normalizeDirName($this->templateDir.'Form');
        $this->templateDirInput = GllHCommon::normalizeDirName($this->templateDir.'Input');
    }

    public function insert($item, $enable = true)
    {
        if (!($item instanceof GllFormItem)) {
            return false;
        }
        return parent::insert($item, $enable);
    }

    public function getPageSettingsContent(&$params)
    {
        if (GllHForm::checkNecessaryFormProcessorParams($params)) {
            $this->initProperty($params[GllHForm::STORAGE]);
            return $this->buildSettingsForms($params);
        }
        return '';
    }

    private function buildSettingsForms(&$params)
    {
        $this->processRequestParams($params);
        return $this->renderForms($params);
    }

    private function isRequestSetDefault($key)
    {
        $requestKey = GllHPrepareName::varnameFormButtonSetDefault($key);
        $value = Tools::getValue($requestKey, false);
        return !!$value;
    }

    private function isRequestSaveMode($key)
    {
        $requestKey = GllHPrepareName::varnameFormButtonSave($key);
        $value = Tools::getValue($requestKey, false);
        return !!$value;
    }

    public function setFormInputsToDefault($keyForm)
    {
        $inputsKeys = $this->getEnableInputs($keyForm);
        foreach ($inputsKeys as $inputKey) {
            /* @var $inputItem GllInputItem */
            $inputItem = $this->inputsContainer->getItem($inputKey);
            $optionKey = $inputItem->getOptionKey();
            $this->optionsContainer->setDefaultValue($optionKey);
        }
    }

    private function processRequestParams(&$params)
    {
        $this->checkedRequest = false;
        $keyForm = GllHForm::getKeyFormFromRequest($this->keysForm);
        if ($keyForm) {
            /* @var $formItem GllFormItem */
            $formItem = $this->getItem($keyForm);
            if ($formItem) {
                $this->checkedRequest = true;
                $this->keyFormRequest = $keyForm;
                if ($this->isRequestSetDefault($keyForm)) {
                    $this->setFormInputsToDefault($keyForm);
                } else {
                    $params[GllHForm::KEYFORM] = $keyForm;
                    $formItem->getRequestProcessor()->process($params);
                }
            }
        }
    }

    private function renderForm($keyForm, &$params)
    {
        $html = '';
        /* @var $formItem GllFormItem */
        $formItem = $this->getItem($keyForm);
        if ($formItem) {
            $hasErrors = $formItem->hasErrors();
            $html .= $this->outputErrors($keyForm, $formItem, $hasErrors);
            $html .= $formItem->getFormBuilder()->process($params);
        }
        return $html;
    }

    private function buildDivTab($id, $active)
    {
        return '<div role="tabpanel" class="tab-pane ssa-tab-pane '.($active ? 'active' : '').'" id="'.$id.'">';
    }

    private function buildLiTab($id, $active, $title)
    {
        $li = '<li role="presentation" '
            .($active ? 'class="active"' : '')
            .'><a href="#'.$id.'" aria-controls="home" role="tab" data-toggle="tab">'.$title.'</a></li>';
        return $li;
    }

    private function renderForms(&$params)
    {
        $htmlTabs = '<ul class="nav nav-tabs ssa-nav-tabs" role="tablist">';
        $html = '';
        $cnt = 0;
        foreach ($this->keysForm as $keyForm) {
            $params[GllHForm::KEYFORM] = $keyForm;
            $htmlForm = $this->renderForm($keyForm, $params);
            if ($htmlForm) {
                $id = 'ssa-form-'.$cnt;
                $title = $this->getItem($keyForm)->getTitle();
                $active = ((!$this->keyFormRequest && !$cnt) || ($this->keyFormRequest === $keyForm));
                $html .= $this->buildDivTab($id, $active).$htmlForm.'</div>';
                $htmlTabs .= $this->buildLiTab($id, $active, $title);
                $cnt++;
            }
        }
        $htmlTabs .= '</ul>';
        return $htmlTabs.'<div class="tab-content ssa-tab-content">'.$html.'</div>';
    }

    protected function outputErrorsForm($keyForm, GllFormItem $formItem, $hasErrors)
    {
        $html = '';
        //Output errors of form
        if ($keyForm === $this->keyFormRequest || $hasErrors) {
            if ($keyForm === $this->keyFormRequest) {
                $this->hasErrorsRequestProcess = $hasErrors;
            }
            $smartyVars = array(
                GllHPrepareName::prepareModuleVarname('form_confirm_tmpl_dir') => $this->templateDirForm,
                GllHPrepareName::prepareModuleVarname('form_confirm_key') => $keyForm,
                GllHPrepareName::prepareModuleVarname('form_confirm_title') => $formItem->getTitle(),
                GllHPrepareName::prepareModuleVarname('form_confirm_status') => ($hasErrors ? 'error' : 'success'),
            );
            $this->context->smarty->assign($smartyVars);
            $mess = trim($this->context->smarty->fetch($this->templateDirForm.'confirm.tpl'));
            if ($mess) {
                $html .= GllHCommon::displayWarning($mess);
            }
            $this->context->smarty->clearAssign(array_keys($smartyVars));
        }
        return $html;
    }

    private function outputErrorsInput($inputKey)
    {
        $html = '';
        /* @var $inputItem GllInputItem */
        $inputItem = $this->inputsContainer->getItem($inputKey);
        $inputErrors = $inputItem->getErrors();
        $mess = array();
        foreach ($inputErrors as $inputError) {
            $oldvalue =
                array_key_exists(GllInputItem::STATUS_OLDVALUE, $inputError)
                ? $inputError[GllInputItem::STATUS_OLDVALUE]
                : null;
            $newvalue =
                array_key_exists(GllInputItem::STATUS_NEWVALUE, $inputError)
                ? $inputError[GllInputItem::STATUS_NEWVALUE]
                : null;
            $smartyVars = array(
                GllHPrepareName::prepareModuleVarname('input_validate_tmpl_dir') => $this->templateDirInput,
                GllHPrepareName::prepareModuleVarname('input_validate_key') => $inputKey,
                GllHPrepareName::prepareModuleVarname('input_validate_label') => $inputItem->getLabel(),
                GllHPrepareName::prepareModuleVarname('input_validate_status') => 'error',
                GllHPrepareName::prepareModuleVarname('input_validate_oldvalue') => $oldvalue,
                GllHPrepareName::prepareModuleVarname('input_validate_newvalue') => $newvalue,
            );
            $this->context->smarty->assign($smartyVars);
            $errorTxt = trim($this->context->smarty->fetch($this->templateDirInput.'validate.tpl'));
            if ($errorTxt) {
                $mess[] = $errorTxt;
            }
            $this->context->smarty->clearAssign(array_keys($smartyVars));
        }
        if (count($mess)) {
            $html .= GllHCommon::displayError($mess);
        }
        return $html;
    }

    private function outputErrors($keyForm, GllFormItem $formItem, $hasErrors)
    {
        $html = $this->outputErrorsForm($keyForm, $formItem, $hasErrors);
        //Output errors of input
        if ($hasErrors) {
            $formErrors = $formItem->getErrors();
            $processedInput = array();
            foreach ($formErrors as $formError) {
                if (! array_key_exists(GllFormItem::STATUS_INPUT_KEY, $formError)
                   || array_key_exists($formError[GllFormItem::STATUS_INPUT_KEY], $processedInput)
                ) {
                    continue;
                }
                $html .= $this->outputErrorsInput($formError[GllFormItem::STATUS_INPUT_KEY]);
                $processedInput[$formError[GllFormItem::STATUS_INPUT_KEY]] = true;
            }
        }
        return $html ? '<div class="ssa-form-errors">'.$html.'</div>' : '';
    }

    public function wasCheckedRequest()
    {
        return $this->checkedRequest;
    }

    public function hasErrorsRequestProcess()
    {
        return ($this->hasErrorsRequestProcess !== false);
    }

    public function getFormItem($keyForm)
    {
        return $this->getItem($keyForm);
    }

    public function getInputsContainer()
    {
        return $this->inputsContainer;
    }

    public function getEnableInputs($keyForm)
    {
        return $this->inputsContainer ? $this->inputsContainer->getEnableFormInputs($keyForm) : array();
    }

    public function getDisableInputs($keyForm)
    {
        return $this->inputsContainer ? $this->inputsContainer->getDisableFormInputs($keyForm) : array();
    }
}
