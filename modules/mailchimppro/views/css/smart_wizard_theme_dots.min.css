/*!
 * SmartWizard v4.3.x
 * jQuery Wizard Plugin
 * http://www.techlaboratory.net/smartwizard
 *
 * Created by <PERSON><PERSON>
 * http://dipuraj.me
 *
 * Licensed under the terms of MIT License
 * https://github.com/techlab/SmartWizard/blob/master/LICENSE
 */
.sw-theme-dots .sw-container {
    min-height: 300px
}

.sw-theme-dots .step-content {
    padding: 10px 0;
    border: none;
    background-color: #fff;
    text-align: left
}

.sw-theme-dots .sw-toolbar {
    background: #fff;
    border-radius: 0 !important;
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 0 !important
}

.sw-theme-dots .sw-toolbar-top {
    border-bottom-color: #ddd !important
}

.sw-theme-dots .sw-toolbar-bottom {
    border-top-color: #ddd !important;
    border-bottom-color: #ddd !important
}

.sw-theme-dots > ul.step-anchor {
    position: relative;
    background: #fff;
    border: 0 solid #ccc !important;
    list-style: none
}

.sw-theme-dots > ul.step-anchor:before {
    content: " ";
    position: absolute;
    top: 70px;
    bottom: 0;
    width: 100%;
    height: 5px;
    background-color: #f5f5f5;
    border-radius: 3px;
    z-order: 0;
    z-index: 95
}

.sw-theme-dots > ul.step-anchor > li {
    border: none
}

.sw-theme-dots > ul.step-anchor > li > a {
    position: relative;
    text-align: center;
    font-weight: 700;
    background: 0 0;
    border: none;
    color: #ccc;
    text-decoration: none;
    outline-style: none;
    z-index: 96;
    display: block
}

.sw-theme-dots > ul.step-anchor > li > a:before {
    content: ' ';
    position: absolute;
    bottom: 2px;
    left: 40%;
    margin-top: 10px;
    display: block;
    border-radius: 50%;
    color: #428bca;
    background: #f5f5f5;
    border: none;
    width: 30px;
    height: 30px;
    text-decoration: none;
    z-index: 98
}

.sw-theme-dots > ul.step-anchor > li > a:after {
    content: ' ';
    position: relative;
    left: 43%;
    bottom: 2px;
    margin-top: 10px;
    display: block;
    width: 15px;
    height: 15px;
    background: #f5f5f5;
    border-radius: 50%;
    z-index: 99
}

.sw-theme-dots > ul.step-anchor > li > a:hover {
    color: #ccc;
    background: 0 0
}

.sw-theme-dots > ul.step-anchor > li > a:focus {
    color: #ccc;
    border: none
}

.sw-theme-dots > ul.step-anchor > li.clickable > a:hover {
    color: #999
}

.sw-theme-dots > ul.step-anchor > li.active > a {
    color: #5bc0de
}

.sw-theme-dots > ul.step-anchor > li.active > a:hover {
    border: none
}

.sw-theme-dots > ul.step-anchor > li.active > a:after {
    background: #5bc0de
}

.sw-theme-dots > ul.step-anchor > li.done > a {
    color: #00C14e
}

.sw-theme-dots > ul.step-anchor > li.done > a:after {
    background: #00C14e
}

.sw-theme-dots > ul.step-anchor > li.danger > a {
    color: #d9534f
}

.sw-theme-dots > ul.step-anchor > li.danger > a:after {
    background: #d9534f
}

.sw-theme-dots > ul.step-anchor > li.disabled > a, .sw-theme-dots > ul.step-anchor > li.disabled > a:hover {
    color: #eee !important
}

.sw-theme-dots > ul.step-anchor > li.disabled > a:after {
    background: #eee
}

@media screen and (max-width: 768px) {
    .sw-theme-dots > ul.step-anchor:before {
        top: 0;
        bottom: 0;
        left: 10px;
        width: 5px;
        height: 100%;
        background-color: #f5f5f5;
        display: block;
        margin-right: 10px
    }

    .sw-theme-dots > ul.step-anchor > li {
        margin-left: 20px;
        display: block;
        clear: both
    }

    .sw-theme-dots > ul.step-anchor > li > a {
        text-align: left;
        margin-left: 0;
        display: block
    }

    .sw-theme-dots > ul.step-anchor > li > a:before {
        top: 5px;
        left: -23px;
        margin-right: 10px;
        display: block
    }

    .sw-theme-dots > ul.step-anchor > li > a:after {
        top: -38px;
        left: -31px;
        margin-right: 10px;
        display: block
    }
}