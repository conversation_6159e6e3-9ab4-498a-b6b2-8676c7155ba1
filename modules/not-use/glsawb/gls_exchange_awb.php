<?php
//error_reporting(0);
include_once('../../config/config.inc.php');
include_once('../../init.php');
require_once(dirname(__FILE__) . '/lib/nusoap.php');
require_once(dirname(__FILE__) . '/glsawb.php');

if (!preg_match('/(webexplorer.ro|nailsup.ro)/', _PS_BASE_URL_)) {
    die();
}


$glsawb_class = new glsawb();
$locatie_gls = Tools::getValue('locatii_awb_gls');
$date_gls_account = $glsawb_class->editGlsLocatieFirma($locatie_gls);

$username_gls = $date_gls_account['gls_user'];
$password_gls = $date_gls_account['gls_password'];
$senderid_gls = $date_gls_account['gls_user_id'];
$gls_interogation_link_gls = trim($date_gls_account['gls_country_link']);
$sender_name_gls = $date_gls_account['gls_company_name'];
$sender_address_gls = $date_gls_account['gls_company_address'];
$sender_city_gls = $date_gls_account['gls_company_city'];
$sender_zipcode_gls = $date_gls_account['gls_company_zipcode'];
$sender_country_gls = $date_gls_account['gls_company_country'];
$sender_phone_gls = $date_gls_account['gls_company_phone'];
$sender_email_gls = $date_gls_account['gls_company_email'];
$gls_preadviceservice_status = Configuration::get('GLSAWB_PREADVICESERVICE');
$gls_flexdeliveryservice_status = Configuration::get('GLSAWB_FLEXDELIVERYSERVICE');
$gls_flexdeliverysmsservice_status = Configuration::get('GLSAWB_FLEXDELIVERYSMSSERVICE');
if(empty(trim($username_gls)) || empty(trim($password_gls)) || empty(trim($senderid_gls)) ) {
  $return = array("errcode" => 9999, "errdesc" => "Completati datele de autentificare API GLS");
  die(json_encode($return, true));
}

$template_listare = Configuration::get('GLS_PRINTER_TEMPLATE');
$numar_listari = $_POST['ref_nr_pachete_awb_gls'];


///////////////// date client ///////////////////////////

$cod_postal_client = $_POST['cod_postal_client_awb_gls'];
$adresa = $_POST['adresa_client_awb_gls'];
$order_id = $_POST['orderid'];
$order = new Order ($order_id);
$customer = new Customer ($order->id_customer);
$adresa_comanda = new Address($order->id_address_delivery);
$city = State::getNameById($_POST['id_judet_awb_gls']);

//$nume_localitate = nume_localitate($_POST['id_localitate_awb_gls']);

$info_cos = new Cart($order->id_cart);
$produse = $info_cos->getProducts();
$email = $customer->email;
$contact_person = $_POST['nume_client_awb_gls'];
$ref_client_awb_gls = trim($_POST['ref_client_awb_gls']);
$ref_ramburs_awb_gls = trim($_POST['ref_ramburs_awb_gls']);

if (!empty($ref_client_awb_gls)) {
    $clientref = glsawb::replace_diacritice($ref_client_awb_gls);
} else {
    $clientref = '';
}
if (!empty($ref_ramburs_awb_gls)) {
    $codref = glsawb::replace_diacritice($ref_ramburs_awb_gls);
} else {
    $codref = '';
}

if (!$adresa_comanda->phone_mobile || empty($adresa_comanda->phone_mobile)) {
    $phone = $adresa_comanda->phone;
} else {
    $phone = $adresa_comanda->phone_mobile;
}


$phone = preg_replace("/[^0-9]/", "", $phone);
$suma_plata = (float)$_POST['ramburs_awb_gls'];
$produse_din_comanda = $order->getProducts(); // se extrag produsele din comanda, deoarece adminul mai poate adauga si alte produse la cele cumparate de client. daca folosim extragere produse din cos, nu se preluau produsele adaugate de admin

$content_comanda = glsawb::creaza_content_comanda($produse_din_comanda, $cookie->id_lang);


////////////////////// date client ///////////////////////////////


$data = date('Y-m-d');
$data_full = date('YmdHis');
$wsdl_path = $gls_interogation_link_gls;
$client = new nusoap_client($wsdl_path, 'wsdl');
$client->soap_defencoding = 'UTF-8';
$client->decode_utf8 = false;

if (!preg_match("~^0\d+$~", $phone)) {
    $phone = "0" . $phone;
}


// servicii active (services)
    $services[] = array('code' => 'XS', "");
if ($gls_preadviceservice_status > 0) {
    $services[] = array('code' => 'SM2', $phone);
}
if ($gls_flexdeliveryservice_status > 0) {
    $services[] = array('code' => 'FDS', $email);
}


$in_merged = array();

    $consig_name_gls = glsawb::replace_diacritice($contact_person);
    $consig_address_gls = glsawb::replace_diacritice($adresa);
    $consig_city_gls = $city;
    $consig_zipcode_gls = $cod_postal_client;
    $consig_country_gls = $sender_country_gls;
    $consig_contact_gls = '';
    $consig_phone_gls = $phone;
    $consig_email_gls = glsawb::replace_diacritice($email);
    $content_gls = $content_comanda;
 
$in = array(

    'username' => $username_gls,
    'password' => $password_gls,
    'senderid' => $senderid_gls,
    'sender_name' => $sender_name_gls,
    'sender_address' => $sender_address_gls,
    'sender_city' => $sender_city_gls,
    'sender_zipcode' => $sender_zipcode_gls,
    'sender_country' => $sender_country_gls,
    'sender_contact' => '',
    'sender_phone' => $sender_phone_gls,
    'sender_email' => $sender_email_gls,


    'consig_name' => $consig_name_gls, 
    'consig_address' => $consig_address_gls, // . ', '. $_POST['id_localitate_awb_gls'],
    'consig_city' => $_POST['id_localitate_awb_gls'],
    'consig_zipcode' => $consig_zipcode_gls,
    'consig_country' => $consig_country_gls,
    'consig_contact' => $consig_contact_gls,
    'consig_phone' => $consig_phone_gls,
    'consig_email' => $consig_email_gls,


    'pcount' => $numar_listari, // nr listari // nr pachete
    'pickupdate' => $data,
    'content' => $content_gls,
    'clientref' => $clientref,
    'codamount' => '' . $suma_plata . '',
    'codref' => $codref, // se activeaza cand un colet are ramburs si cee ace este specificat aici apare in desfasuratorul de rambursuri.
    'services' => $services,
    'printertemplate' => $template_listare,
    'printit' => true,
    'timestamp' => $data_full,
    'hash' => 'xsd:string',
    'customlabel' => false
);

$in_merged[0] = $in;
//print_r($in);

$in_exchange = array(

    'username' => $username_gls,
    'password' => $password_gls,
    'senderid' => $senderid_gls,
    'sender_name' => $consig_name_gls,
    'sender_address' => $consig_address_gls, 
    'sender_city' => $_POST['id_localitate_awb_gls'],
    'sender_zipcode' => $consig_zipcode_gls,
    'sender_country' => $consig_country_gls,
    'sender_contact' => $consig_contact_gls,
    'sender_phone' => $consig_phone_gls,
    'sender_email' => $consig_email_gls,


    'consig_name' => $sender_name_gls,
    'consig_address' => $sender_address_gls, // . ', '. $_POST['id_localitate_awb_gls'],
    'consig_city' => $sender_city_gls,
    'consig_zipcode' => $sender_zipcode_gls,
    'consig_country' => $sender_country_gls,
    'consig_contact' => '',
    'consig_phone' => $sender_phone_gls,
    'consig_email' => $sender_email_gls,


    'pcount' => 1, // nr listari // nr pachete
    'pickupdate' => $data,
    'content' => "Retur",
    'clientref' => '',
    'codamount' => "0",
    'codref' => '', // se activeaza cand un colet are ramburs si cee ace este specificat aici apare in desfasuratorul de rambursuri.
    'services' => $services,
    'printertemplate' => $template_listare,
    'printit' => true,
    'timestamp' => $data_full,
    'hash' => 'xsd:string',
    'customlabel' => false
);

$in_merged[1] = $in_exchange;

foreach($in_merged as $nr => $info) { // start foreach
  $prefix_awb = "";
  $info['hash'] = '';
  $info['hash'] = glsawb::getHash($info);
  $return = $client->call('printlabel', $info);
    if ($return) {// start if return
        if($nr == 1) {
            $prefix_awb = "exchange_";
        }
        if ($return['pdfdata']) { // start if pdf exist
        // start salvam nr awb
        $nr_awb = (int)$return['pcls'][0];
 
            $sql_check = 'SELECT COUNT(id)  FROM `' . _DB_PREFIX_ . 'gls_awburi` WHERE `id_order` = ' . $order_id;
            $nr = (int)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql_check);
                Db::getInstance()->execute("INSERT INTO `" . _DB_PREFIX_ . "gls_awburi` (`id_order`, `gls_user_id`, `numar_awb`, `status_awb`, `awb_date_added`) VALUES (" . $order_id . ", "  .$senderid_gls . ", '" . $prefix_awb.(int)$nr_awb . "', 'Manifestat. Nepreluat', now())");
                if($nr == 0) {
                   glsawb::save_tracking_number_to_order($order_id, $nr_awb);
                }

                sleep(1);
                if(Configuration::get('GLSAWB_SEND_CUSTOMER_EMAIL') == 1) {
                   if($nr == 1) {
                      glsawb::SendCustomerGlsAWB((int)$nr_awb, $consig_name_gls, $order->reference, "G.L.S.", $consig_email_gls, 'awbexchange');
                   }
                }

 
        $pdf_path = dirname(__FILE__) . "/awburi/";
        $fp = fopen($pdf_path . (int)$nr_awb . ".pdf", "w");
        fwrite($fp, base64_decode($return['pdfdata']));
        fclose($fp);
        
      } // end if pdf exist

    }//end if return 
  $info = array();
} // end foreach

 
if(@$return['errcode']) {
die(json_encode($return, true));
} else {
die(json_encode(glsawb::BuildHtmlMenu($order_id), true));
}

?>