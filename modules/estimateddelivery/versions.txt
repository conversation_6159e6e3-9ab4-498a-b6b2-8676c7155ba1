*Estimated Deliery
**Changelog history

1.0.0 First release
1.1.0 Better compatibility with Prestashop 1.5.x versions
1.1.1 Added dropshipping compatibility
1.1.2 Added CSS Tooltip Display
1.1.3 Code optimization
1.1.4 Bug Fixing and same delivery date displayed only once
1.1.5 Now carriers can be disabled individually through the Product > Shipping tab
1.2.0 Added Standard delivery time for non defined carriers & Better carrier sorting
1.2.1 Added locale detection, fixed and encoding bug
1.2.2 Better Date Sorting
1.2.3 Better Locale detection code
1.2.5 Small bug in JS variations without stock fixed
1.3.0 Better locale detection system for correctly displaying the Dates
1.3.2 More date format options
1.3.3 Make improvements on the State detection
2.0.0 Added mode options and individual control, new display system, carrier alias, carrier disable...
2.0.1 Improved Spain Province detection system
2.0.2 Now Estimated Delivery prints relative dates like Today and Tomorrow
2.0.3 Added a secondary Geolocation system
2.0.4 Some bugs fixed
2.0.5 Tabs Content Added
2.0.6 Added a thid Geolocation system, now once a user is geolocated it stores a cookie to make browsing faster
2.0.7 Added a tooltip switch for themes that doesn't support it
2.0.8 Fixed a bug on Sundays
2.0.9 Minor fixes
2.1.0 Better compatibility for 1.5.X versions
2.1.1 Better Warehouse detection
2.1.2 Feature requested: Added product / category OOS days
2.1.3 Bug Fixes
2.1.4 Improved the amazon display style
2.1.5 Better combination detection and OOS days
2.1.6 Minor Bug Fixes
2.1.7 Instant delivery for virtual products
2.1.8 Added available date for preorder produts
2.1.8 Fixed a bug in date calculation on combinations with Oos
2.2.0 Added an advanced mode for debugging and override
2.2.1 Improved coding for ubication detection
2.2.1 Improved combination OOS days calculationfunc
2.2.1 Bugs fixed
2.2.2 Improved date calculation on oos days, code optimization
2.2.2 Better Multi-shop support
2.2.3 Code improvement
2.2.3 Added Beta feature, ED on product lists
2.2.4 Added increase picking days by product feature
2.2.5 UX and functionality improved for 1.5
2.2.5 Take into account user group show_price option
2.2.6 Improved the state detection and covered failure cases
2.2.7 Module update reviews - Reapply missing updates
2.2.7 Better locale encoding
2.3.0 Add Estimated Prices to Estimated Delivery
2.3.0 Compatible with Presta 1.7.X
2.5.0 Added the ED to order process
2.5.0 Estimated delivery is saved on the database
2.5.0 Estimated delivery can be accessed through BO and in the Order History
2.5.1 Bugs Fixed
2.5.1 Better stock detection for product listing
2.5.1 Estimated Delviery on orders can be modified through the BO
2.5.1 Added Italian Translation
2.5.1 Added Deutsche Translation
2.5.1 Added Portuguese Translation
2.5.1 Added Dutch Translation
2.5.2 Compatible with SuperCheckout Module
2.5.2 Individual and shorter template for ED in product Listings
2.5.3 Fixed a bug in Holidays calculation
2.5.4 Small bug fix on OOS product stock detection
2.5.4 Fixed a small issue with products out os stock with slaes enabled in checkout process
2.5.5 Fixed a small bug on stock detection for PS 1.7
2.5.5 Fixed a bug on picking calculation when picking and shipping did not match
2.5.5 Better massive select and unselect for section 2.2 and 2.3
2.5.6 Improved ED cart JS
2.5.6 Better multi-shop support
2.5.6 Improved French translation
2.5.7 Better OOS detection on Product Lists
2.5.7 Better OOS performance on Product Pages
2.5.7 Better Calculation for ED in the order process when there more products than stock (and sales enabled)
2.5.8 Updated to be compatible with Presta *******
2.5.8 Fixed a bug for shops not using the stock managment feature
2.5.8 Added Spanish MX Translation
2.5.8 Added Spanish CL Translation
2.5.8 Added Czech Translation
2.5.8 Added Norweigan Translation
2.5.9 Added Additional picking days by manufacturer
2.6.0 ED on product List feature for Prestashop 1.7
2.6.1 Added Additional picking days by manufacturer
2.6.2 Added more options to ED in Product Listing
2.6.3 Fixed a small bug on ED in order when product has 1 stock
2.6.4 Small bug fixed on holidays (in cart)
2.6.4 New configuration options for the order process
2.6.5 New options to display an advice for those orders who does have a longer delivery date
2.6.5 Added availability_date notice in Prestashop 1.7 for a product with a future available date and without stock
2.7.0 Individual carrier settings allowed for each shop (multi-shop sites)
2.7.1 Better ED in order process
2.7.1 Improved code for ED in products listing
2.7.1 Improved Combination change detect in PS 1.7
2.7.2 Added an option to force disable the ED for OOS products
2.7.2 More positioning options for 1.7.X versions
2.7.3 Improved multishop brand / supplier additional picking days behaviour
2.7.3 Improved module's 3rd display stile to have a better structure and performance on date calculation.
2.7.4 Category Exclude from ED
2.7.4 Disable ED now breaks the Calculation process if one of the items in the cart can't be computed to display a delivery
3.0.0 Code renewal
3.0.0 Improved Combination detection system
3.0.0 Module implementation to be able to work at combination level
3.0.0 ED in product list now available in PS 1.7.X (may need adjustments depending on the theme)
3.0.0 ED in the email templates (Add it with just a line of code!, won't work if you already have an override in the PaymentModule class)
3.0.1 Fixed an Installation issue
3.0.1 Fixed a bug on Global OOS add days
3.0.2 Added OOS days by supplier
3.0.2 Bug fixes in complex combination additional day calculations
3.0.2 Add reset procedure to Reset the parameters and hooks but keeping the vacations saved
3.0.2 Code improvement for free carriers
3.0.2 Improved getProductCarriers
3.0.2 Fixed an issue with advanced carriers
3.0.2 Added the product's minimal quantity to the OOS calculation
3.0.2 Added new format for the date in the product list.
3.0.3 Improved Combination detection PS 1.7
3.0.3 Added carrier weight restrictions to limit the carriers for the ED
3.0.3 Improved the free shipping detection conditions
3.0.3 Corrected the calculation for validate order for orders with the exact amount as the remaining shop's stock
3.0.3 Fixed Holidays add new
3.0.4 Improved compatibility with older versions in order process
3.0.5 Small fix on Configuration page
3.0.5 Fixed a bug on Price calculation giving false "free shipping" prices
3.0.6 Improved Preorder and Available later ED in order process
3.0.6 Improved OPC PS compatibility
3.0.6 Force all carriers to be enabled in the order process
3.0.7 Changed the Delivery class name to avoid conflicts with other modules
3.0.7 Fixed an issue on confirmation dates for products oos
3.0.7 Fixed an issue on Product additional delivery days
3.0.7 Fixed an issue with picking time calculation
3.0.8 Added Brand for OOS Days
3.0.8 DataBase tables restructuration to save resources and optimize procedures
3.0.8 Small fix on displaying carriers on cart
3.0.8 Established a limit of 0.08 seconds to check and retrieve the data from the IP
3.0.8 Small improvement for Products with combinations using the Order Before style for PrestaShop 1.6
3.0.9 Several improvements on LDA Feature (Long Delivery Advice)
3.0.9 Improved SQL for zone to prevent it from returnning id = 0
3.0.9 Added Ignore Picking option for carriers
3.0.10 Improved the order delivery time calculation
3.0.10 Fixed a small issue with combinations
3.0.10 Improved additional picking days
3.1.0 Fixed an issue with delivery dates when changing an order state
3.1.1 Improved installation procedure
3.1.1 Improved ED in email templates, the override is no needed anymore to display the ED in the emails.
3.1.1 Improved the ED Date update procedure when a order state changes
3.1.1 New options to manually update date from the Order History panel in the Back Office
3.1.2 Improved PrestaShop 1.5 compatibility
3.1.2 Solved an issue in holidays calculation
3.1.2 Improved module's debug mode to not interfere with ajax procedures
3.1.3 Improved override remove procedure for updating versions
3.1.3 Added compatibility with One Page Checkout module (onepagecheckout)
3.1.3 Added a date format selector for email messages
3.1.4 Fixed a translation issue with library items not using the specific feature