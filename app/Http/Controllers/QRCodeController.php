<?php

namespace App\Http\Controllers;

use App\Events\QrCodeScannedEvent;
use App\Models\QRCode;
use App\Models\User;
use App\Models\UserQrCode;
use Grosv\LaravelPasswordlessLogin\LoginUrl;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\View\View;
use SimpleSoftwareIO\QrCode\Facades;

class QRCodeController extends Controller
{
    public function getQrData()
    {

    }

    public function qrTest(): View
    {
        return view('qr-test');
    }

    public function sendLoginLink(Request $request): JsonResponse
    {
        $qr_code = QRCode::query()->whereQrCode($request->code)->first();
        if ($qr_code && $qr_code->expires_at->gte(now()->addMinutes(config('qr_codes.expiration')))) {
            $user = User::query()->whereId(auth()->id())->first();

            if (! UserQrCode::query()->whereQrCodeId($qr_code->id)->exists()) {
                UserQrCode::query()->create([
                    'user_id' => $user->id,
                    'qr_code_id' => $qr_code->id,
                ]);
            }

            $generator = new LoginUrl($user);
            $generator->setRedirectUrl(route('student.dashboard'));
            $url = $generator->generate();

            QrCodeScannedEvent::dispatch($qr_code->qr_code, $url);

            return response()->json([
                'url' => $url,
            ]);
        }

        return response()->json([
            'error' => 'Invalid QR Code!',
        ], 500);

    }

    public function generateQrCode(): JsonResponse
    {

        $qr_string = Hash::make(Str::random(config('qr_code.code_length')));
        $qr_code_html = Facades\QrCode::size(210)->generate($qr_string);

        $qr_code = QRCode::query()->create([
            'qr_code' => $qr_string,
            'expires_at' => now()->addMinutes(config('qr_code.expiration')),
        ]);

        return response()->json([
            'qr_code' => $qr_code,
            'qr_code_html' => $qr_code_html->toHtml(),
        ]);
    }
}
